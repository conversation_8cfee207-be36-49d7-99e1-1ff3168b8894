<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚拟机管理 - VMware 虚拟机自动部署平台</title>
    <link rel="stylesheet" href="css/normalize.css">
    <link rel="stylesheet" href="css/style.css">
    <!-- 添加一个简单的网站图标 -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💻</text></svg>">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="container navbar-container">
            <a href="dashboard.html" class="navbar-brand">
                <!-- 使用内联SVG作为临时logo -->
                <svg width="120" height="32" viewBox="0 0 120 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="32" height="32" rx="4" fill="white" fill-opacity="0.2"/>
                    <path d="M8 16L16 8L24 16L16 24L8 16Z" fill="white"/>
                    <path d="M16 8V24" stroke="white" stroke-width="2"/>
                    <path d="M8 16H24" stroke="white" stroke-width="2"/>
                    <text x="36" y="22" font-family="'Segoe UI', Arial, sans-serif" font-size="14" font-weight="600" fill="white">VM Deploy</text>
                </svg>
            </a>
            <ul class="navbar-nav">
                <li class="navbar-item">
                    <a href="#" class="navbar-link">帮助</a>
                </li>
                <li class="navbar-item">
                    <a href="#" class="navbar-link">设置</a>
                </li>
                <li class="navbar-item">
                    <a href="index.html" class="navbar-link">退出</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <ul class="sidebar-nav">
            <li class="sidebar-item">
                <a href="dashboard.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="3" y="3" width="7" height="7"></rect>
                        <rect x="14" y="3" width="7" height="7"></rect>
                        <rect x="14" y="14" width="7" height="7"></rect>
                        <rect x="3" y="14" width="7" height="7"></rect>
                    </svg>
                    仪表盘
                </a>
            </li>
            <li class="sidebar-item">
                <a href="templates.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10 9 9 9 8 9"></polyline>
                    </svg>
                    模板库
                </a>
            </li>
            <li class="sidebar-item">
                <a href="topology.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="2" y1="12" x2="22" y2="12"></line>
                        <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                    </svg>
                    拓扑设计器
                </a>
            </li>
            <li class="sidebar-item">
                <a href="deploy.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                        <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                        <line x1="12" y1="22.08" x2="12" y2="12"></line>
                    </svg>
                    部署中心
                </a>
            </li>
            <li class="sidebar-item">
                <a href="batch-deploy.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="9" y="2" width="6" height="6"></rect>
                        <rect x="16" y="16" width="6" height="6"></rect>
                        <rect x="2" y="16" width="6" height="6"></rect>
                        <path d="M5 16v-4h14v4"></path>
                        <path d="M12 12V8"></path>
                    </svg>
                    批量部署
                </a>
            </li>
            <li class="sidebar-item">
                <a href="machines.html" class="sidebar-link active">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                        <line x1="8" y1="21" x2="16" y2="21"></line>
                        <line x1="12" y1="17" x2="12" y2="21"></line>
                    </svg>
                    虚拟机管理
                </a>
            </li>
            <li class="sidebar-item">
                <a href="monitoring.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                    </svg>
                    资源监控
                </a>
            </li>
            <li class="sidebar-item">
                <a href="users.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                    用户管理
                </a>
            </li>
        </ul>
    </aside>

    <!-- 主内容区域 -->
    <main class="main">
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                <h1>虚拟机管理</h1>
                <a href="deploy.html" class="btn btn-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 8px; vertical-align: text-bottom;">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    新建虚拟机
                </a>
            </div>

            <!-- 搜索和筛选 -->
            <div class="card" style="margin-bottom: 24px;">
                <div class="card-body" style="padding: 16px;">
                    <div style="display: flex; gap: 16px; flex-wrap: wrap;">
                        <div style="flex: 1; min-width: 200px;">
                            <label for="search" class="form-label">搜索虚拟机</label>
                            <input type="text" id="search" class="form-control" placeholder="输入虚拟机名称...">
                        </div>
                        <div style="flex: 1; min-width: 200px;">
                            <label for="status-filter" class="form-label">状态</label>
                            <select id="status-filter" class="form-control">
                                <option value="">全部</option>
                                <option value="running">运行中</option>
                                <option value="stopped">已停止</option>
                                <option value="paused">已暂停</option>
                            </select>
                        </div>
                        <div style="flex: 1; min-width: 200px;">
                            <label for="os-filter" class="form-label">操作系统</label>
                            <select id="os-filter" class="form-control">
                                <option value="">全部</option>
                                <option value="windows">Windows</option>
                                <option value="ubuntu">Ubuntu</option>
                                <option value="centos">CentOS</option>
                                <option value="debian">Debian</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 虚拟机列表 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">虚拟机列表</h2>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>名称</th>
                                    <th>模板</th>
                                    <th>IP地址</th>
                                    <th>CPU/内存/磁盘</th>
                                    <th>创建时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>web-server-01</td>
                                    <td>Ubuntu Server 22.04</td>
                                    <td>*************</td>
                                    <td>2核 / 4GB / 50GB</td>
                                    <td>2023-10-15 14:30</td>
                                    <td><span class="status status-running">运行中</span></td>
                                    <td>
                                        <div style="display: flex; gap: 8px;">
                                            <button class="btn" style="padding: 4px 8px; font-size: 12px; background-color: var(--bg-light); border-color: var(--border-color);">停止</button>
                                            <button class="btn" style="padding: 4px 8px; font-size: 12px; background-color: var(--bg-light); border-color: var(--border-color);">重启</button>
                                            <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">管理</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>db-server-01</td>
                                    <td>CentOS 8</td>
                                    <td>192.168.1.102</td>
                                    <td>4核 / 8GB / 100GB</td>
                                    <td>2023-10-15 13:45</td>
                                    <td><span class="status status-running">运行中</span></td>
                                    <td>
                                        <div style="display: flex; gap: 8px;">
                                            <button class="btn" style="padding: 4px 8px; font-size: 12px; background-color: var(--bg-light); border-color: var(--border-color);">停止</button>
                                            <button class="btn" style="padding: 4px 8px; font-size: 12px; background-color: var(--bg-light); border-color: var(--border-color);">重启</button>
                                            <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">管理</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>test-windows-01</td>
                                    <td>Windows Server 2019</td>
                                    <td>192.168.1.103</td>
                                    <td>4核 / 16GB / 250GB</td>
                                    <td>2023-10-14 16:20</td>
                                    <td><span class="status status-stopped">已停止</span></td>
                                    <td>
                                        <div style="display: flex; gap: 8px;">
                                            <button class="btn" style="padding: 4px 8px; font-size: 12px; background-color: var(--bg-light); border-color: var(--border-color);">启动</button>
                                            <button class="btn" style="padding: 4px 8px; font-size: 12px; background-color: var(--bg-light); border-color: var(--border-color);" disabled>重启</button>
                                            <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">管理</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>dev-env-01</td>
                                    <td>Debian 11</td>
                                    <td>192.168.1.104</td>
                                    <td>2核 / 4GB / 50GB</td>
                                    <td>2023-10-14 10:15</td>
                                    <td><span class="status status-paused">已暂停</span></td>
                                    <td>
                                        <div style="display: flex; gap: 8px;">
                                            <button class="btn" style="padding: 4px 8px; font-size: 12px; background-color: var(--bg-light); border-color: var(--border-color);">恢复</button>
                                            <button class="btn" style="padding: 4px 8px; font-size: 12px; background-color: var(--bg-light); border-color: var(--border-color);">停止</button>
                                            <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">管理</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>win10-desktop-01</td>
                                    <td>Windows 10 Pro</td>
                                    <td>192.168.1.105</td>
                                    <td>4核 / 8GB / 100GB</td>
                                    <td>2023-10-13 09:30</td>
                                    <td><span class="status status-running">运行中</span></td>
                                    <td>
                                        <div style="display: flex; gap: 8px;">
                                            <button class="btn" style="padding: 4px 8px; font-size: 12px; background-color: var(--bg-light); border-color: var(--border-color);">停止</button>
                                            <button class="btn" style="padding: 4px 8px; font-size: 12px; background-color: var(--bg-light); border-color: var(--border-color);">重启</button>
                                            <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px;">管理</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 批量操作 -->
            <div style="margin-top: 24px; display: flex; gap: 16px;">
                <select class="form-control" style="width: auto;">
                    <option value="">批量操作</option>
                    <option value="start">启动</option>
                    <option value="stop">停止</option>
                    <option value="restart">重启</option>
                    <option value="delete">删除</option>
                </select>
                <button class="btn" style="background-color: var(--bg-light); border-color: var(--border-color);">应用</button>
            </div>

            <!-- 分页 -->
            <div style="display: flex; justify-content: center; margin-top: 24px;">
                <div style="display: flex; gap: 8px;">
                    <a href="#" style="width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; border: 1px solid var(--border-color); border-radius: var(--radius); color: var(--text-light);">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="15 18 9 12 15 6"></polyline>
                        </svg>
                    </a>
                    <a href="#" style="width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; border: 1px solid var(--border-color); border-radius: var(--radius); background-color: var(--primary-color); color: white;">1</a>
                    <a href="#" style="width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; border: 1px solid var(--border-color); border-radius: var(--radius); color: var(--text-dark);">2</a>
                    <a href="#" style="width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; border: 1px solid var(--border-color); border-radius: var(--radius); color: var(--text-light);">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="9 18 15 12 9 6"></polyline>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </main>
</body>
</html> 