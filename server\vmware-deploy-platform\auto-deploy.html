<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一键部署 - 网络系统管理竞赛自动化运维平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f5f7fa;
            color: #2c3e50;
        }

        /* 导航栏 */
        .navbar {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            font-size: 20px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .navbar-user {
            display: flex;
            align-items: center;
            gap: 16px;
            color: #666;
            font-size: 14px;
        }

        .navbar-user a {
            color: #3182ce;
            text-decoration: none;
        }

        /* 侧边栏 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            bottom: 0;
            width: 240px;
            background: white;
            border-right: 1px solid #e2e8f0;
            padding: 24px 0;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 4px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.2s;
            font-size: 14px;
        }

        .sidebar-menu a:hover {
            background: #f7fafc;
            color: #1a1a1a;
        }

        .sidebar-menu a.active {
            background: #e6f2ff;
            color: #3182ce;
            font-weight: 500;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 240px;
            padding: 32px;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-header h1 {
            font-size: 28px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 8px;
        }

        .page-header p {
            color: #666;
            font-size: 14px;
        }

        /* 部署选项 */
        .deploy-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .deploy-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 24px;
            transition: all 0.2s;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .deploy-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .deploy-card.selected {
            border-color: #3182ce;
            background: #f0f9ff;
        }

        .deploy-card-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .deploy-icon {
            font-size: 32px;
        }

        .deploy-title {
            flex: 1;
        }

        .deploy-title h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 4px;
        }

        .deploy-title p {
            font-size: 13px;
            color: #666;
        }

        .deploy-features {
            list-style: none;
            margin-top: 16px;
        }

        .deploy-features li {
            padding: 8px 0;
            font-size: 14px;
            color: #4a5568;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .deploy-features li::before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
        }

        /* 部署配置 */
        .deploy-config {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 32px;
            margin-bottom: 24px;
        }

        .config-section {
            margin-bottom: 32px;
        }

        .config-section:last-child {
            margin-bottom: 0;
        }

        .config-section h3 {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 16px;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #4a5568;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.2s;
            background: #fafbfc;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3182ce;
            background: white;
        }

        /* 部署按钮 */
        .deploy-actions {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-top: 32px;
        }

        .btn {
            padding: 14px 32px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #1a1a1a;
            color: white;
        }

        .btn-primary:hover {
            background: #2d2d2d;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: white;
            color: #1a1a1a;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #f7fafc;
        }

        .btn-large {
            padding: 18px 48px;
            font-size: 18px;
        }

        /* 进度显示 */
        .deploy-progress {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 32px;
            display: none;
        }

        .progress-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .progress-header h3 {
            font-size: 24px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 8px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 24px;
        }

        .progress-fill {
            height: 100%;
            background: #3182ce;
            transition: width 0.3s;
            width: 0%;
        }

        .progress-steps {
            list-style: none;
        }

        .progress-step {
            padding: 16px;
            border-left: 3px solid #e2e8f0;
            margin-left: 8px;
            position: relative;
        }

        .progress-step.active {
            border-color: #3182ce;
        }

        .progress-step.completed {
            border-color: #10b981;
        }

        .progress-step::before {
            content: "";
            position: absolute;
            left: -8px;
            top: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e2e8f0;
        }

        .progress-step.active::before {
            background: #3182ce;
        }

        .progress-step.completed::before {
            background: #10b981;
        }

        .step-title {
            font-size: 16px;
            font-weight: 500;
            color: #1a1a1a;
            margin-bottom: 4px;
        }

        .step-status {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="navbar-brand">网络系统管理竞赛自动化运维平台</div>
        <div class="navbar-user">
            <span>管理员</span>
            <a href="index.html">退出</a>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html">📊 仪表板</a></li>
            <li><a href="competition-env.html">🏆 竞赛环境管理</a></li>
            <li><a href="auto-deploy.html" class="active">🚀 一键部署</a></li>
            <li><a href="batch-workstation.html">💻 工位批量管理</a></li>
            <li><a href="scoring-system.html">📝 自动评分系统</a></li>
            <li><a href="fault-management.html">🔧 故障排除模块</a></li>
            <li><a href="module-management.html">📦 模块管理</a></li>
            <li><a href="environment-restore.html">🔄 环境还原</a></li>
            <li><a href="monitoring.html">📈 监控中心</a></li>
        </ul>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="page-header">
            <h1>一键自动化部署</h1>
            <p>快速部署网络系统管理项目基础设施编程与自动化运维模块竞赛环境</p>
        </div>

        <!-- 部署选项 -->
        <div class="deploy-options">
            <div class="deploy-card selected" onclick="selectDeployOption(this)">
                <div class="deploy-card-header">
                    <span class="deploy-icon">🏆</span>
                    <div class="deploy-title">
                        <h3>竞赛环境部署</h3>
                        <p>部署完整的竞赛环境，包含所有必需模块</p>
                    </div>
                </div>
                <ul class="deploy-features">
                    <li>基础设施编程模块</li>
                    <li>自动化运维模块</li>
                    <li>网络配置与管理</li>
                    <li>自动评分系统集成</li>
                    <li>故障排除功能支持</li>
                </ul>
            </div>

            <div class="deploy-card" onclick="selectDeployOption(this)">
                <div class="deploy-card-header">
                    <span class="deploy-icon">🎓</span>
                    <div class="deploy-title">
                        <h3>训练环境部署</h3>
                        <p>适合日常训练和教学使用的环境</p>
                    </div>
                </div>
                <ul class="deploy-features">
                    <li>基础练习环境</li>
                    <li>分阶段训练模块</li>
                    <li>实时监控与反馈</li>
                    <li>资源使用优化</li>
                    <li>灵活配置选项</li>
                </ul>
            </div>

            <div class="deploy-card" onclick="selectDeployOption(this)">
                <div class="deploy-card-header">
                    <span class="deploy-icon">🔧</span>
                    <div class="deploy-title">
                        <h3>自定义部署</h3>
                        <p>根据特定需求自定义部署配置</p>
                    </div>
                </div>
                <ul class="deploy-features">
                    <li>模块自由选择</li>
                    <li>参数个性化配置</li>
                    <li>网络拓扑定制</li>
                    <li>资源弹性分配</li>
                    <li>高级功能设置</li>
                </ul>
            </div>
        </div>

        <!-- 部署配置 -->
        <div class="deploy-config">
            <div class="config-section">
                <h3>基础配置</h3>
                <div class="config-grid">
                    <div class="form-group">
                        <label for="env-name">环境名称</label>
                        <input type="text" id="env-name" value="WSC2025-竞赛环境" placeholder="输入环境名称">
                    </div>
                    <div class="form-group">
                        <label for="workstation-count">工位数量</label>
                        <input type="number" id="workstation-count" value="100" min="1" max="200" placeholder="1-200">
                    </div>
                    <div class="form-group">
                        <label for="deploy-mode">部署模式</label>
                        <select id="deploy-mode">
                            <option value="parallel">并行部署（快速）</option>
                            <option value="sequential">顺序部署（稳定）</option>
                            <option value="batch">批次部署（资源优化）</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h3>模块选择</h3>
                <div class="config-grid">
                    <div class="form-group">
                        <label>
                            <input type="checkbox" checked> 基础设施编程模块
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" checked> 自动化运维模块
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" checked> 网络系统管理模块
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" checked> 故障排除模块
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" checked> 自动评分系统
                        </label>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox"> 扩展功能模块
                        </label>
                    </div>
                </div>
            </div>

            <div class="config-section">
                <h3>资源配置</h3>
                <div class="config-grid">
                    <div class="form-group">
                        <label for="cpu-per-workstation">每工位CPU核心数</label>
                        <select id="cpu-per-workstation">
                            <option value="2">2 核心</option>
                            <option value="4" selected>4 核心</option>
                            <option value="8">8 核心</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="memory-per-workstation">每工位内存(GB)</label>
                        <select id="memory-per-workstation">
                            <option value="4">4 GB</option>
                            <option value="8" selected>8 GB</option>
                            <option value="16">16 GB</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="disk-per-workstation">每工位磁盘空间(GB)</label>
                        <select id="disk-per-workstation">
                            <option value="50">50 GB</option>
                            <option value="100" selected>100 GB</option>
                            <option value="200">200 GB</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 部署按钮 -->
        <div class="deploy-actions">
            <button class="btn btn-primary btn-large" onclick="startDeploy()">
                <span>🚀</span>
                <span>开始一键部署</span>
            </button>
            <button class="btn btn-secondary" onclick="alert('功能演示：保存当前配置为模板，方便下次快速使用')">
                <span>💾</span>
                <span>保存配置</span>
            </button>
        </div>

        <!-- 部署进度 -->
        <div class="deploy-progress" id="deployProgress">
            <div class="progress-header">
                <h3>正在部署竞赛环境...</h3>
                <p>预计需要 15-20 分钟，请耐心等待</p>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <ul class="progress-steps">
                <li class="progress-step active" id="step1">
                    <div class="step-title">环境初始化</div>
                    <div class="step-status">正在准备部署环境...</div>
                </li>
                <li class="progress-step" id="step2">
                    <div class="step-title">基础设施部署</div>
                    <div class="step-status">等待中...</div>
                </li>
                <li class="progress-step" id="step3">
                    <div class="step-title">模块安装</div>
                    <div class="step-status">等待中...</div>
                </li>
                <li class="progress-step" id="step4">
                    <div class="step-title">配置优化</div>
                    <div class="step-status">等待中...</div>
                </li>
                <li class="progress-step" id="step5">
                    <div class="step-title">完成部署</div>
                    <div class="step-status">等待中...</div>
                </li>
            </ul>
        </div>
    </div>

    <script>
        function selectDeployOption(element) {
            // 移除所有选中状态
            document.querySelectorAll('.deploy-card').forEach(card => {
                card.classList.remove('selected');
            });
            // 添加选中状态
            element.classList.add('selected');
        }

        function startDeploy() {
            if (confirm('确定要开始一键部署吗？这将部署100套工位的竞赛环境。')) {
                // 隐藏配置区域
                document.querySelector('.deploy-options').style.display = 'none';
                document.querySelector('.deploy-config').style.display = 'none';
                document.querySelector('.deploy-actions').style.display = 'none';
                
                // 显示进度
                document.getElementById('deployProgress').style.display = 'block';
                
                // 模拟部署进度
                simulateDeployProgress();
            }
        }

        function simulateDeployProgress() {
            let progress = 0;
            const steps = [
                { id: 'step1', status: '初始化完成' },
                { id: 'step2', status: '基础设施部署完成' },
                { id: 'step3', status: '模块安装完成' },
                { id: 'step4', status: '配置优化完成' },
                { id: 'step5', status: '部署成功！' }
            ];
            
            let currentStep = 0;
            
            const interval = setInterval(() => {
                progress += 2;
                document.getElementById('progressFill').style.width = progress + '%';
                
                if (progress >= (currentStep + 1) * 20 && currentStep < steps.length) {
                    // 完成当前步骤
                    const step = document.getElementById(steps[currentStep].id);
                    step.classList.remove('active');
                    step.classList.add('completed');
                    step.querySelector('.step-status').textContent = steps[currentStep].status;
                    
                    // 激活下一步骤
                    if (currentStep < steps.length - 1) {
                        currentStep++;
                        const nextStep = document.getElementById(steps[currentStep].id);
                        nextStep.classList.add('active');
                        nextStep.querySelector('.step-status').textContent = '正在处理...';
                    }
                }
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        alert('部署完成！100套工位的竞赛环境已成功部署。\n\n部署详情：\n- 基础设施编程模块 ✓\n- 自动化运维模块 ✓\n- 网络系统管理模块 ✓\n- 故障排除模块 ✓\n- 自动评分系统 ✓');
                        window.location.href = 'dashboard.html';
                    }, 1000);
                }
            }, 100);
        }
    </script>
</body>
</html>
