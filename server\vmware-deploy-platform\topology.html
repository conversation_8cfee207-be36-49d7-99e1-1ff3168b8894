<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>拓扑设计器 - VMware Workstation 虚拟机自动部署平台</title>
  <link rel="stylesheet" href="css/normalize.css">
  <link rel="stylesheet" href="css/style.css">
  <link rel="stylesheet" href="css/topology.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container container">
      <a href="dashboard.html" class="navbar-brand">
        <img src="images/logo.png" alt="Logo">
        VMware 虚拟机自动部署平台
      </a>
      <ul class="navbar-nav">
        <li class="navbar-item">
          <a href="#" class="navbar-link">通知</a>
        </li>
        <li class="navbar-item">
          <a href="#" class="navbar-link">帮助</a>
        </li>
        <li class="navbar-item">
          <a href="index.html" class="navbar-link">退出</a>
        </li>
      </ul>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <aside class="sidebar">
    <ul class="sidebar-nav">
      <li class="sidebar-item">
        <a href="dashboard.html" class="sidebar-link">
          <span class="sidebar-icon">📊</span>
          仪表盘
        </a>
      </li>
      <li class="sidebar-item">
        <a href="templates.html" class="sidebar-link">
          <span class="sidebar-icon">📁</span>
          模板库
        </a>
      </li>
      <li class="sidebar-item">
        <a href="topology.html" class="sidebar-link active">
          <span class="sidebar-icon">🔄</span>
          拓扑设计器
        </a>
      </li>
      <li class="sidebar-item">
        <a href="deploy.html" class="sidebar-link">
          <span class="sidebar-icon">🚀</span>
          部署中心
        </a>
      </li>
      <li class="sidebar-item">
        <a href="batch-deploy.html" class="sidebar-link">
          <span class="sidebar-icon">📦</span>
          批量部署
        </a>
      </li>
      <li class="sidebar-item">
        <a href="machines.html" class="sidebar-link">
          <span class="sidebar-icon">💻</span>
          虚拟机管理
        </a>
      </li>
      <li class="sidebar-item">
        <a href="monitoring.html" class="sidebar-link">
          <span class="sidebar-icon">📈</span>
          资源监控
        </a>
      </li>
      <li class="sidebar-item">
        <a href="users.html" class="sidebar-link">
          <span class="sidebar-icon">👥</span>
          用户管理
        </a>
      </li>
    </ul>
  </aside>

  <!-- 主内容区域 -->
  <main class="main">
    <div class="container">
      <h1>拓扑设计器</h1>
      <p>设计您的虚拟网络拓扑，然后一键部署到多台客户端。</p>

      <!-- 拓扑元数据 -->
      <div class="topology-metadata">
        <div class="row">
          <div class="col">
            <div class="form-group">
              <label for="topology-name" class="form-label">拓扑名称</label>
              <input type="text" id="topology-name" class="form-control" placeholder="输入拓扑名称">
            </div>
          </div>
          <div class="col">
            <div class="form-group">
              <label for="topology-description" class="form-label">描述</label>
              <input type="text" id="topology-description" class="form-control" placeholder="简要描述此拓扑的用途">
            </div>
          </div>
        </div>
      </div>

      <!-- 拓扑设计器容器 -->
      <div class="topology-container">
        <!-- 组件侧边栏 -->
        <div class="component-sidebar">
          <div class="component-header">组件库</div>
          <div class="component-library">
            <!-- 组件将由JavaScript动态加载 -->
          </div>
        </div>

        <!-- 拓扑画布容器 -->
        <div class="topology-canvas-container">
          <!-- 工具栏 -->
          <div class="topology-toolbar">
            <div class="toolbar-group">
              <button id="save-topology" class="btn btn-primary toolbar-item">保存拓扑</button>
              <button id="load-topology" class="btn toolbar-item">加载拓扑</button>
              <button id="validate-topology" class="btn toolbar-item">验证拓扑</button>
            </div>
            <div class="topology-info">
              <div class="topology-info-item">节点: <span id="node-count">0</span></div>
              <div class="topology-info-item">连接: <span id="connection-count">0</span></div>
            </div>
          </div>

          <!-- 拓扑画布 -->
          <canvas id="topology-canvas"></canvas>

          <!-- 缩放控件 -->
          <div class="zoom-controls">
            <button class="zoom-button" id="zoom-in">+</button>
            <button class="zoom-button" id="zoom-reset">⟳</button>
            <button class="zoom-button" id="zoom-out">-</button>
          </div>
        </div>

        <!-- 属性面板 -->
        <div class="properties-panel">
          <p>选择一个节点或连接以查看属性</p>
        </div>
      </div>

      <!-- 部署按钮 -->
      <div style="margin-top: 20px; text-align: right;">
        <button id="deploy-topology" class="btn btn-primary">部署拓扑</button>
      </div>

      <!-- 使用说明 -->
      <div class="card" style="margin-top: 24px;">
        <div class="card-header">
          <h2 class="card-title">使用说明</h2>
        </div>
        <div class="card-body">
          <h3>基本操作</h3>
          <ul>
            <li>从左侧组件库拖拽组件到画布上创建节点</li>
            <li>点击节点选中它，在右侧面板编辑其属性</li>
            <li>按住 Ctrl 键并从一个节点拖动到另一个节点创建连接</li>
            <li>点击连接线选中它，在右侧面板编辑其属性</li>
            <li>点击空白处取消选择</li>
          </ul>

          <h3>键盘快捷键</h3>
          <ul>
            <li><strong>Delete</strong>: 删除选中的节点或连接</li>
            <li><strong>Ctrl+S</strong>: 保存当前拓扑</li>
            <li><strong>Ctrl+O</strong>: 加载拓扑</li>
            <li><strong>Ctrl+Z</strong>: 撤销上一步操作</li>
            <li><strong>Ctrl+Y</strong>: 重做操作</li>
          </ul>

          <h3>拓扑部署</h3>
          <p>点击"部署拓扑"按钮将当前设计的拓扑提交到批量部署页面，您可以在那里选择要部署的客户端机器。</p>
        </div>
      </div>
    </div>
  </main>

  <script src="js/topology-designer.js"></script>
</body>
</html> 