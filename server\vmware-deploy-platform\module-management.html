<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块管理 - 网络系统管理竞赛自动化运维平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f5f7fa;
            color: #2c3e50;
        }

        /* 导航栏 */
        .navbar {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            font-size: 20px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .navbar-user {
            display: flex;
            align-items: center;
            gap: 16px;
            color: #666;
            font-size: 14px;
        }

        .navbar-user a {
            color: #3182ce;
            text-decoration: none;
        }

        /* 侧边栏 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            bottom: 0;
            width: 240px;
            background: white;
            border-right: 1px solid #e2e8f0;
            padding: 24px 0;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 4px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.2s;
            font-size: 14px;
        }

        .sidebar-menu a:hover {
            background: #f7fafc;
            color: #1a1a1a;
        }

        .sidebar-menu a.active {
            background: #e6f2ff;
            color: #3182ce;
            font-weight: 500;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 240px;
            padding: 32px;
        }

        .page-header {
            margin-bottom: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-header h1 {
            font-size: 28px;
            font-weight: 600;
            color: #1a1a1a;
        }

        /* 模块统计 */
        .module-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stat-card h3 {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .stat-card .value {
            font-size: 28px;
            font-weight: 600;
            color: #1a1a1a;
        }

        /* 模块网格 */
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .module-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.2s;
        }

        .module-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .module-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .module-info {
            flex: 1;
        }

        .module-name {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 4px;
        }

        .module-version {
            font-size: 14px;
            color: #666;
        }

        .module-status {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #d1fae5;
            color: #10b981;
        }

        .status-inactive {
            background: #f3f4f6;
            color: #6b7280;
        }

        .status-updating {
            background: #dbeafe;
            color: #3b82f6;
        }

        .module-body {
            padding: 20px;
        }

        .module-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .module-features {
            list-style: none;
            margin-bottom: 16px;
        }

        .module-features li {
            font-size: 13px;
            color: #4a5568;
            padding: 4px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .module-features li::before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
        }

        .module-actions {
            display: flex;
            gap: 8px;
            padding-top: 16px;
            border-top: 1px solid #f1f5f9;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            flex: 1;
            justify-content: center;
        }

        .btn-primary {
            background: #1a1a1a;
            color: white;
        }

        .btn-primary:hover {
            background: #2d2d2d;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: white;
            color: #1a1a1a;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #f7fafc;
        }

        .btn-danger {
            background: white;
            color: #ef4444;
            border: 1px solid #fee2e2;
        }

        .btn-danger:hover {
            background: #fee2e2;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="navbar-brand">网络系统管理竞赛自动化运维平台</div>
        <div class="navbar-user">
            <span>管理员</span>
            <a href="index.html">退出</a>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html">📊 仪表板</a></li>
            <li><a href="competition-env.html">🏆 竞赛环境管理</a></li>
            <li><a href="auto-deploy.html">🚀 一键部署</a></li>
            <li><a href="batch-workstation.html">💻 工位批量管理</a></li>
            <li><a href="scoring-system.html">📝 自动评分系统</a></li>
            <li><a href="fault-management.html">🔧 故障排除模块</a></li>
            <li><a href="module-management.html" class="active">📦 模块管理</a></li>
            <li><a href="environment-restore.html">🔄 环境还原</a></li>
            <li><a href="monitoring.html">📈 监控中心</a></li>
        </ul>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="page-header">
            <h1>模块管理</h1>
            <button class="btn btn-primary" onclick="alert('功能演示：上传新模块')">
                <span>⬆️</span>
                <span>上传模块</span>
            </button>
        </div>

        <!-- 模块统计 -->
        <div class="module-stats">
            <div class="stat-card">
                <h3>总模块数</h3>
                <div class="value">12</div>
            </div>
            <div class="stat-card">
                <h3>已激活</h3>
                <div class="value">9</div>
            </div>
            <div class="stat-card">
                <h3>待更新</h3>
                <div class="value">2</div>
            </div>
            <div class="stat-card">
                <h3>自定义模块</h3>
                <div class="value">3</div>
            </div>
        </div>

        <!-- 模块列表 -->
        <div class="modules-grid">
            <div class="module-card">
                <div class="module-header">
                    <div class="module-info">
                        <h3 class="module-name">基础设施编程模块</h3>
                        <p class="module-version">版本: 2.1.0</p>
                    </div>
                    <span class="module-status status-active">已激活</span>
                </div>
                <div class="module-body">
                    <p class="module-description">
                        支持Python、Shell、Ansible等脚本编写和执行，提供基础设施自动化管理能力
                    </p>
                    <ul class="module-features">
                        <li>Python脚本执行环境</li>
                        <li>Shell命令支持</li>
                        <li>Ansible剧本管理</li>
                        <li>自动化任务调度</li>
                    </ul>
                    <div class="module-actions">
                        <button class="btn btn-secondary" onclick="alert('功能演示：查看模块详情')">详情</button>
                        <button class="btn btn-secondary" onclick="alert('功能演示：配置模块')">配置</button>
                        <button class="btn btn-danger" onclick="alert('功能演示：停用模块')">停用</button>
                    </div>
                </div>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <div class="module-info">
                        <h3 class="module-name">自动化运维模块</h3>
                        <p class="module-version">版本: 3.0.1</p>
                    </div>
                    <span class="module-status status-active">已激活</span>
                </div>
                <div class="module-body">
                    <p class="module-description">
                        提供CI/CD流程支持，容器化部署，自动化测试等DevOps工具集成
                    </p>
                    <ul class="module-features">
                        <li>Jenkins集成</li>
                        <li>Docker容器管理</li>
                        <li>Kubernetes编排</li>
                        <li>自动化测试框架</li>
                    </ul>
                    <div class="module-actions">
                        <button class="btn btn-secondary" onclick="alert('功能演示：查看模块详情')">详情</button>
                        <button class="btn btn-secondary" onclick="alert('功能演示：配置模块')">配置</button>
                        <button class="btn btn-danger" onclick="alert('功能演示：停用模块')">停用</button>
                    </div>
                </div>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <div class="module-info">
                        <h3 class="module-name">网络系统管理模块</h3>
                        <p class="module-version">版本: 1.8.5</p>
                    </div>
                    <span class="module-status status-updating">更新中</span>
                </div>
                <div class="module-body">
                    <p class="module-description">
                        网络配置管理、服务部署、系统监控和性能优化工具集
                    </p>
                    <ul class="module-features">
                        <li>网络配置自动化</li>
                        <li>服务健康检查</li>
                        <li>性能监控分析</li>
                        <li>日志集中管理</li>
                    </ul>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="alert('功能演示：查看更新进度')">更新进度</button>
                        <button class="btn btn-secondary" onclick="alert('功能演示：取消更新')">取消</button>
                    </div>
                </div>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <div class="module-info">
                        <h3 class="module-name">故障排除模块</h3>
                        <p class="module-version">版本: 2.5.0</p>
                    </div>
                    <span class="module-status status-active">已激活</span>
                </div>
                <div class="module-body">
                    <p class="module-description">
                        智能故障诊断、问题定位、自动修复和预防性维护功能
                    </p>
                    <ul class="module-features">
                        <li>故障自动诊断</li>
                        <li>问题根因分析</li>
                        <li>自动修复脚本</li>
                        <li>预防性检查</li>
                    </ul>
                    <div class="module-actions">
                        <button class="btn btn-secondary" onclick="alert('功能演示：查看模块详情')">详情</button>
                        <button class="btn btn-secondary" onclick="alert('功能演示：配置模块')">配置</button>
                        <button class="btn btn-danger" onclick="alert('功能演示：停用模块')">停用</button>
                    </div>
                </div>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <div class="module-info">
                        <h3 class="module-name">安全加固模块</h3>
                        <p class="module-version">版本: 1.2.0</p>
                    </div>
                    <span class="module-status status-inactive">未激活</span>
                </div>
                <div class="module-body">
                    <p class="module-description">
                        系统安全加固、漏洞扫描、合规性检查和安全策略管理
                    </p>
                    <ul class="module-features">
                        <li>安全基线检查</li>
                        <li>漏洞扫描修复</li>
                        <li>访问控制管理</li>
                        <li>安全日志审计</li>
                    </ul>
                    <div class="module-actions">
                        <button class="btn btn-primary" onclick="alert('功能演示：激活模块')">激活</button>
                        <button class="btn btn-secondary" onclick="alert('功能演示：查看详情')">详情</button>
                    </div>
                </div>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <div class="module-info">
                        <h3 class="module-name">自定义脚本模块</h3>
                        <p class="module-version">版本: 自定义</p>
                    </div>
                    <span class="module-status status-active">已激活</span>
                </div>
                <div class="module-body">
                    <p class="module-description">
                        用户自定义的脚本和工具集成，支持特定需求的功能扩展
                    </p>
                    <ul class="module-features">
                        <li>自定义脚本管理</li>
                        <li>API接口集成</li>
                        <li>第三方工具支持</li>
                        <li>插件化架构</li>
                    </ul>
                    <div class="module-actions">
                        <button class="btn btn-secondary" onclick="alert('功能演示：编辑模块')">编辑</button>
                        <button class="btn btn-secondary" onclick="alert('功能演示：导出模块')">导出</button>
                        <button class="btn btn-danger" onclick="alert('功能演示：删除模块')">删除</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
