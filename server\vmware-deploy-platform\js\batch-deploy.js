/**
 * VMware Workstation 批量部署管理
 * 提供多客户端虚拟机统一部署功能
 */

// 客户端和部署任务数据
let clients = [];
let deploymentTasks = [];
let selectedClients = [];
let pendingTopology = null;

// 初始化批量部署管理器
function initBatchDeployManager() {
    // 加载客户端列表
    loadClients();
    
    // 加载部署任务
    loadDeploymentTasks();
    
    // 检查是否有待部署的拓扑
    checkPendingTopology();
    
    // 注册事件监听器
    registerEventListeners();
}

// 加载客户端列表
function loadClients() {
    // 在实际应用中，这里应该从服务器获取客户端列表
    // 这里使用模拟数据
    clients = [
        { id: 'client-001', name: '开发服务器01', ip: '*************', status: 'online', resources: { cpu: 16, memory: 32768, disk: 1000 } },
        { id: 'client-002', name: '开发服务器02', ip: '*************', status: 'online', resources: { cpu: 16, memory: 32768, disk: 1000 } },
        { id: 'client-003', name: '测试服务器01', ip: '*************', status: 'online', resources: { cpu: 8, memory: 16384, disk: 500 } },
        { id: 'client-004', name: '测试服务器02', ip: '*************', status: 'offline', resources: { cpu: 8, memory: 16384, disk: 500 } },
        { id: 'client-005', name: '生产服务器01', ip: '*************', status: 'online', resources: { cpu: 32, memory: 65536, disk: 2000 } }
    ];
    
    // 渲染客户端列表
    renderClientList();
}

// 加载部署任务
function loadDeploymentTasks() {
    // 在实际应用中，这里应该从服务器获取部署任务列表
    // 这里使用模拟数据
    deploymentTasks = [
        {
            id: 'task-001',
            name: '开发环境部署',
            status: 'completed',
            createdAt: '2023-07-05T10:30:00',
            completedAt: '2023-07-05T11:15:00',
            clients: ['client-001', 'client-002'],
            vms: 5,
            success: 5,
            failed: 0
        },
        {
            id: 'task-002',
            name: '测试环境部署',
            status: 'in_progress',
            createdAt: '2023-07-08T09:15:00',
            clients: ['client-003'],
            vms: 3,
            success: 1,
            failed: 0
        },
        {
            id: 'task-003',
            name: '网络实验环境',
            status: 'failed',
            createdAt: '2023-07-01T14:20:00',
            completedAt: '2023-07-01T14:35:00',
            clients: ['client-004'],
            vms: 4,
            success: 2,
            failed: 2
        }
    ];
    
    // 渲染部署任务列表
    renderDeploymentTaskList();
}

// 检查是否有待部署的拓扑
function checkPendingTopology() {
    const pendingTopologyData = localStorage.getItem('pendingTopologyDeploy');
    
    if (pendingTopologyData) {
        try {
            pendingTopology = JSON.parse(pendingTopologyData);
            
            // 显示待部署拓扑信息
            document.getElementById('pending-topology-container').style.display = 'block';
            document.getElementById('pending-topology-name').textContent = pendingTopology.name;
            document.getElementById('pending-topology-nodes').textContent = pendingTopology.nodes.length;
            document.getElementById('pending-topology-connections').textContent = pendingTopology.connections.length;
            
            // 显示拓扑预览图
            renderTopologyPreview(pendingTopology);
        } catch (error) {
            console.error('无法解析待部署拓扑数据:', error);
            localStorage.removeItem('pendingTopologyDeploy');
        }
    } else {
        document.getElementById('pending-topology-container').style.display = 'none';
    }
}

// 渲染拓扑预览图
function renderTopologyPreview(topology) {
    const previewCanvas = document.getElementById('topology-preview');
    if (!previewCanvas) return;
    
    const ctx = previewCanvas.getContext('2d');
    const width = previewCanvas.width;
    const height = previewCanvas.height;
    
    // 清除画布
    ctx.clearRect(0, 0, width, height);
    
    // 如果没有节点，直接返回
    if (!topology.nodes || topology.nodes.length === 0) return;
    
    // 计算节点位置的边界
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    
    topology.nodes.forEach(node => {
        minX = Math.min(minX, node.x);
        minY = Math.min(minY, node.y);
        maxX = Math.max(maxX, node.x);
        maxY = Math.max(maxY, node.y);
    });
    
    // 计算缩放比例
    const padding = 20;
    const scaleX = (width - padding * 2) / (maxX - minX || 1);
    const scaleY = (height - padding * 2) / (maxY - minY || 1);
    const scale = Math.min(scaleX, scaleY);
    
    // 绘制连接
    if (topology.connections) {
        topology.connections.forEach(conn => {
            const sourceNode = topology.nodes.find(n => n.id === conn.sourceId);
            const targetNode = topology.nodes.find(n => n.id === conn.targetId);
            
            if (sourceNode && targetNode) {
                const sx = padding + (sourceNode.x - minX) * scale;
                const sy = padding + (sourceNode.y - minY) * scale;
                const tx = padding + (targetNode.x - minX) * scale;
                const ty = padding + (targetNode.y - minY) * scale;
                
                ctx.beginPath();
                ctx.moveTo(sx, sy);
                ctx.lineTo(tx, ty);
                
                // 根据连接类型设置样式
                switch(conn.type) {
                    case 'lan':
                        ctx.strokeStyle = '#2ecc71';
                        break;
                    case 'wan':
                        ctx.strokeStyle = '#e74c3c';
                        break;
                    default:
                        ctx.strokeStyle = '#3498db';
                }
                
                ctx.lineWidth = 1;
                ctx.stroke();
            }
        });
    }
    
    // 绘制节点
    topology.nodes.forEach(node => {
        const x = padding + (node.x - minX) * scale;
        const y = padding + (node.y - minY) * scale;
        
        // 绘制节点圆形
        ctx.beginPath();
        ctx.arc(x, y, 5, 0, Math.PI * 2);
        
        // 根据节点类型设置颜色
        if (node.type === 'network') {
            ctx.fillStyle = '#3498db';
        } else {
            ctx.fillStyle = '#e74c3c';
        }
        
        ctx.fill();
    });
}

// 注册事件监听器
function registerEventListeners() {
    // 客户端选择
    document.querySelectorAll('.client-select-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', handleClientSelection);
    });
    
    // 部署按钮
    const deployButton = document.getElementById('deploy-topology-btn');
    if (deployButton) {
        deployButton.addEventListener('click', deployTopologyToSelectedClients);
    }
    
    // 取消部署按钮
    const cancelButton = document.getElementById('cancel-topology-btn');
    if (cancelButton) {
        cancelButton.addEventListener('click', cancelPendingTopology);
    }
    
    // 客户端过滤
    const filterInput = document.getElementById('client-filter');
    if (filterInput) {
        filterInput.addEventListener('input', filterClientList);
    }
    
    // 客户端分组选择
    const groupSelect = document.getElementById('client-group-filter');
    if (groupSelect) {
        groupSelect.addEventListener('change', filterClientList);
    }
    
    // 全选/取消全选按钮
    const selectAllBtn = document.getElementById('select-all-clients');
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', toggleSelectAllClients);
    }
    
    // 任务详情按钮
    document.querySelectorAll('.view-task-details').forEach(btn => {
        btn.addEventListener('click', viewTaskDetails);
    });
}

// 渲染客户端列表
function renderClientList() {
    const clientListContainer = document.getElementById('client-list');
    if (!clientListContainer) return;
    
    clientListContainer.innerHTML = '';
    
    clients.forEach(client => {
        const clientElement = document.createElement('div');
        clientElement.className = `client-item ${client.status === 'online' ? 'online' : 'offline'}`;
        clientElement.setAttribute('data-client-id', client.id);
        
        clientElement.innerHTML = `
            <div class="client-select">
                <input type="checkbox" id="client-${client.id}" class="client-select-checkbox" 
                       data-client-id="${client.id}" ${client.status === 'offline' ? 'disabled' : ''}>
            </div>
            <div class="client-info">
                <div class="client-name">${client.name}</div>
                <div class="client-ip">${client.ip}</div>
                <div class="client-status ${client.status}">${client.status === 'online' ? '在线' : '离线'}</div>
            </div>
            <div class="client-resources">
                <div class="resource-item">
                    <span class="resource-icon">CPU</span>
                    <span class="resource-value">${client.resources.cpu} 核</span>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">RAM</span>
                    <span class="resource-value">${client.resources.memory / 1024} GB</span>
                </div>
                <div class="resource-item">
                    <span class="resource-icon">存储</span>
                    <span class="resource-value">${client.resources.disk} GB</span>
                </div>
            </div>
        `;
        
        clientListContainer.appendChild(clientElement);
    });
    
    // 重新绑定事件
    document.querySelectorAll('.client-select-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', handleClientSelection);
    });
}

// 渲染部署任务列表
function renderDeploymentTaskList() {
    const taskListContainer = document.getElementById('deployment-tasks');
    if (!taskListContainer) return;
    
    taskListContainer.innerHTML = '';
    
    deploymentTasks.forEach(task => {
        const taskElement = document.createElement('div');
        taskElement.className = `task-item ${task.status}`;
        taskElement.setAttribute('data-task-id', task.id);
        
        // 格式化日期
        const createdDate = new Date(task.createdAt);
        const formattedCreatedDate = `${createdDate.getFullYear()}-${String(createdDate.getMonth() + 1).padStart(2, '0')}-${String(createdDate.getDate()).padStart(2, '0')} ${String(createdDate.getHours()).padStart(2, '0')}:${String(createdDate.getMinutes()).padStart(2, '0')}`;
        
        // 状态文本
        let statusText = '';
        switch (task.status) {
            case 'completed':
                statusText = '已完成';
                break;
            case 'in_progress':
                statusText = '进行中';
                break;
            case 'failed':
                statusText = '失败';
                break;
            default:
                statusText = '未知';
        }
        
        taskElement.innerHTML = `
            <div class="task-header">
                <div class="task-name">${task.name}</div>
                <div class="task-status ${task.status}">${statusText}</div>
            </div>
            <div class="task-details">
                <div class="task-info">
                    <div class="task-created">创建时间: ${formattedCreatedDate}</div>
                    <div class="task-clients">目标客户端: ${task.clients.length}</div>
                </div>
                <div class="task-progress">
                    <div class="progress-bar">
                        <div class="progress-success" style="width: ${task.success / task.vms * 100}%"></div>
                        <div class="progress-failed" style="width: ${task.failed / task.vms * 100}%"></div>
                    </div>
                    <div class="progress-text">${task.success}/${task.vms} 虚拟机已部署</div>
                </div>
                <div class="task-actions">
                    <button class="view-task-details" data-task-id="${task.id}">查看详情</button>
                    ${task.status === 'in_progress' ? '<button class="cancel-task" data-task-id="' + task.id + '">取消</button>' : ''}
                </div>
            </div>
        `;
        
        taskListContainer.appendChild(taskElement);
    });
    
    // 重新绑定事件
    document.querySelectorAll('.view-task-details').forEach(btn => {
        btn.addEventListener('click', viewTaskDetails);
    });
    
    document.querySelectorAll('.cancel-task').forEach(btn => {
        btn.addEventListener('click', cancelTask);
    });
}

// 处理客户端选择
function handleClientSelection(e) {
    const clientId = e.target.getAttribute('data-client-id');
    
    if (e.target.checked) {
        // 添加到选中列表
        if (!selectedClients.includes(clientId)) {
            selectedClients.push(clientId);
        }
    } else {
        // 从选中列表中移除
        const index = selectedClients.indexOf(clientId);
        if (index !== -1) {
            selectedClients.splice(index, 1);
        }
    }
    
    // 更新部署按钮状态
    updateDeployButtonState();
}

// 更新部署按钮状态
function updateDeployButtonState() {
    const deployButton = document.getElementById('deploy-topology-btn');
    if (deployButton) {
        deployButton.disabled = selectedClients.length === 0 || !pendingTopology;
    }
}

// 部署拓扑到选中的客户端
function deployTopologyToSelectedClients() {
    if (selectedClients.length === 0 || !pendingTopology) {
        alert('请选择至少一个客户端，并确保有待部署的拓扑');
        return;
    }
    
    // 创建新的部署任务
    const newTask = {
        id: 'task-' + Date.now(),
        name: pendingTopology.name || '未命名拓扑部署',
        status: 'in_progress',
        createdAt: new Date().toISOString(),
        clients: [...selectedClients],
        vms: pendingTopology.nodes.filter(node => node.type === 'vm').length * selectedClients.length,
        success: 0,
        failed: 0
    };
    
    // 添加到任务列表
    deploymentTasks.unshift(newTask);
    
    // 渲染任务列表
    renderDeploymentTaskList();
    
    // 清除待部署拓扑
    localStorage.removeItem('pendingTopologyDeploy');
    pendingTopology = null;
    document.getElementById('pending-topology-container').style.display = 'none';
    
    // 清除选中的客户端
    selectedClients = [];
    document.querySelectorAll('.client-select-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    
    // 更新部署按钮状态
    updateDeployButtonState();
    
    // 显示部署开始消息
    alert(`已开始在 ${newTask.clients.length} 个客户端上部署 ${newTask.name}，共 ${newTask.vms} 个虚拟机`);
    
    // 模拟部署进度更新
    simulateDeploymentProgress(newTask.id);
}

// 模拟部署进度更新
function simulateDeploymentProgress(taskId) {
    const task = deploymentTasks.find(t => t.id === taskId);
    if (!task || task.status !== 'in_progress') return;
    
    // 模拟部署进度
    const totalVMs = task.vms;
    let deployedVMs = 0;
    let failedVMs = 0;
    
    const progressInterval = setInterval(() => {
        // 随机决定是否部署成功
        const success = Math.random() > 0.1; // 90% 成功率
        
        if (success) {
            deployedVMs++;
        } else {
            failedVMs++;
        }
        
        // 更新任务状态
        task.success = deployedVMs;
        task.failed = failedVMs;
        
        // 渲染任务列表
        renderDeploymentTaskList();
        
        // 检查是否完成
        if (deployedVMs + failedVMs >= totalVMs) {
            clearInterval(progressInterval);
            
            // 更新任务状态
            task.status = failedVMs === 0 ? 'completed' : 'failed';
            task.completedAt = new Date().toISOString();
            
            // 渲染任务列表
            renderDeploymentTaskList();
        }
    }, 1000); // 每秒更新一次
}

// 取消待部署拓扑
function cancelPendingTopology() {
    if (confirm('确定要取消当前待部署的拓扑吗？')) {
        localStorage.removeItem('pendingTopologyDeploy');
        pendingTopology = null;
        document.getElementById('pending-topology-container').style.display = 'none';
        
        // 更新部署按钮状态
        updateDeployButtonState();
    }
}

// 过滤客户端列表
function filterClientList() {
    const filterText = document.getElementById('client-filter').value.toLowerCase();
    const groupFilter = document.getElementById('client-group-filter').value;
    
    document.querySelectorAll('.client-item').forEach(clientItem => {
        const clientId = clientItem.getAttribute('data-client-id');
        const client = clients.find(c => c.id === clientId);
        
        if (!client) return;
        
        const nameMatch = client.name.toLowerCase().includes(filterText) || 
                          client.ip.toLowerCase().includes(filterText);
        
        let groupMatch = true;
        if (groupFilter === 'online') {
            groupMatch = client.status === 'online';
        } else if (groupFilter === 'offline') {
            groupMatch = client.status === 'offline';
        }
        
        clientItem.style.display = nameMatch && groupMatch ? 'flex' : 'none';
    });
}

// 全选/取消全选客户端
function toggleSelectAllClients() {
    const selectAllBtn = document.getElementById('select-all-clients');
    const isSelectAll = selectAllBtn.textContent === '全选';
    
    // 更新按钮文本
    selectAllBtn.textContent = isSelectAll ? '取消全选' : '全选';
    
    // 更新复选框状态
    document.querySelectorAll('.client-select-checkbox:not([disabled])').forEach(checkbox => {
        checkbox.checked = isSelectAll;
        
        // 触发change事件
        const event = new Event('change');
        checkbox.dispatchEvent(event);
    });
}

// 查看任务详情
function viewTaskDetails(e) {
    const taskId = e.target.getAttribute('data-task-id');
    const task = deploymentTasks.find(t => t.id === taskId);
    
    if (!task) return;
    
    // 在实际应用中，这里应该打开一个模态框显示详细信息
    // 这里简单地使用alert显示
    
    // 格式化日期
    const createdDate = new Date(task.createdAt);
    const formattedCreatedDate = `${createdDate.getFullYear()}-${String(createdDate.getMonth() + 1).padStart(2, '0')}-${String(createdDate.getDate()).padStart(2, '0')} ${String(createdDate.getHours()).padStart(2, '0')}:${String(createdDate.getMinutes()).padStart(2, '0')}`;
    
    let completedDateText = '';
    if (task.completedAt) {
        const completedDate = new Date(task.completedAt);
        completedDateText = `${completedDate.getFullYear()}-${String(completedDate.getMonth() + 1).padStart(2, '0')}-${String(completedDate.getDate()).padStart(2, '0')} ${String(completedDate.getHours()).padStart(2, '0')}:${String(completedDate.getMinutes()).padStart(2, '0')}`;
    }
    
    // 状态文本
    let statusText = '';
    switch (task.status) {
        case 'completed':
            statusText = '已完成';
            break;
        case 'in_progress':
            statusText = '进行中';
            break;
        case 'failed':
            statusText = '失败';
            break;
        default:
            statusText = '未知';
    }
    
    // 获取客户端名称
    const clientNames = task.clients.map(clientId => {
        const client = clients.find(c => c.id === clientId);
        return client ? client.name : clientId;
    }).join('\n- ');
    
    const detailsText = `
任务名称: ${task.name}
状态: ${statusText}
创建时间: ${formattedCreatedDate}
${task.completedAt ? '完成时间: ' + completedDateText : ''}
目标客户端:
- ${clientNames}
虚拟机总数: ${task.vms}
成功部署: ${task.success}
部署失败: ${task.failed}
    `;
    
    alert(detailsText);
}

// 取消任务
function cancelTask(e) {
    const taskId = e.target.getAttribute('data-task-id');
    const task = deploymentTasks.find(t => t.id === taskId);
    
    if (!task || task.status !== 'in_progress') return;
    
    if (confirm(`确定要取消任务"${task.name}"吗？`)) {
        // 更新任务状态
        task.status = 'failed';
        task.completedAt = new Date().toISOString();
        
        // 渲染任务列表
        renderDeploymentTaskList();
    }
}

// 页面加载时初始化
window.addEventListener('load', initBatchDeployManager); 