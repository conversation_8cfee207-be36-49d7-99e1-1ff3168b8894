/* 拓扑设计器专用样式 */

/* 连接线样式 */
.connection-lan {
  stroke: #2ecc71;
  stroke-width: 2;
}

.connection-wan {
  stroke: #e74c3c;
  stroke-width: 2;
}

.connection-default {
  stroke: #3498db;
  stroke-width: 2;
}

.connection-selected {
  stroke: #9b59b6;
  stroke-width: 4;
}

.connection-creating {
  stroke: #3498db;
  stroke-width: 2;
  stroke-dasharray: 5, 3;
}

/* 节点样式 */
.node {
  cursor: move;
}

.node-vm {
  fill: #e74c3c;
}

.node-network {
  fill: #3498db;
}

.node-selected {
  stroke: #3498db;
  stroke-width: 2;
}

/* 画布网格背景 */
.grid-line {
  stroke: #e0e0e0;
  stroke-width: 0.5;
}

/* 工具提示 */
.topology-tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
  white-space: nowrap;
}

/* 迷你地图 */
.minimap {
  position: absolute;
  bottom: 16px;
  right: 16px;
  width: 200px;
  height: 150px;
  background-color: white;
  border: 1px solid #dadce0;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.minimap-viewport {
  position: absolute;
  border: 2px solid #1a73e8;
  background-color: rgba(26, 115, 232, 0.1);
  cursor: move;
}

/* 缩放控件 */
.zoom-controls {
  position: absolute;
  bottom: 16px;
  left: 16px;
  display: flex;
  flex-direction: column;
  background-color: white;
  border: 1px solid #dadce0;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.zoom-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: none;
  border-bottom: 1px solid #dadce0;
  cursor: pointer;
}

.zoom-button:last-child {
  border-bottom: none;
}

.zoom-button:hover {
  background-color: #f1f3f4;
}

/* 上下文菜单 */
.context-menu {
  position: absolute;
  background-color: white;
  border: 1px solid #dadce0;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 150px;
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
}

.context-menu-item:hover {
  background-color: #f1f3f4;
}

.context-menu-divider {
  height: 1px;
  background-color: #dadce0;
  margin: 4px 0;
}

/* 拓扑验证结果 */
.validation-result {
  position: absolute;
  top: 16px;
  right: 16px;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 900;
  animation: fade-in 0.3s ease-in-out;
  max-width: 300px;
}

.validation-success {
  background-color: rgba(52, 168, 83, 0.1);
  border: 1px solid #34a853;
  color: #34a853;
}

.validation-error {
  background-color: rgba(234, 67, 53, 0.1);
  border: 1px solid #ea4335;
  color: #ea4335;
}

@keyframes fade-in {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 拖拽指示器 */
.drag-indicator {
  position: absolute;
  pointer-events: none;
  z-index: 1000;
  width: 32px;
  height: 32px;
  background-color: white;
  border: 1px solid #dadce0;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 连接点 */
.connection-point {
  fill: white;
  stroke: #3498db;
  stroke-width: 2;
  cursor: pointer;
}

.connection-point:hover {
  fill: #3498db;
}

/* 节点标签 */
.node-label {
  font-size: 12px;
  text-anchor: middle;
  pointer-events: none;
}

/* 连接标签 */
.connection-label {
  font-size: 11px;
  text-anchor: middle;
  pointer-events: none;
  background-color: white;
  padding: 2px 4px;
  border-radius: 2px;
}

/* 拓扑保存/加载对话框 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
}

.dialog {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 400px;
}

.dialog-header {
  padding: 16px;
  border-bottom: 1px solid #dadce0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dialog-title {
  font-size: 18px;
  font-weight: 500;
}

.dialog-close {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 20px;
  color: #5f6368;
}

.dialog-body {
  padding: 16px;
}

.dialog-footer {
  padding: 16px;
  border-top: 1px solid #dadce0;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
} 