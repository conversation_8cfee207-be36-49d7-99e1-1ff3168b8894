<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源监控 - VMware 虚拟机自动部署平台</title>
    <link rel="stylesheet" href="css/normalize.css">
    <link rel="stylesheet" href="css/style.css">
    <!-- 添加一个简单的网站图标 -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💻</text></svg>">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="container navbar-container">
            <a href="dashboard.html" class="navbar-brand">
                <!-- 使用内联SVG作为临时logo -->
                <svg width="120" height="32" viewBox="0 0 120 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="32" height="32" rx="4" fill="white" fill-opacity="0.2"/>
                    <path d="M8 16L16 8L24 16L16 24L8 16Z" fill="white"/>
                    <path d="M16 8V24" stroke="white" stroke-width="2"/>
                    <path d="M8 16H24" stroke="white" stroke-width="2"/>
                    <text x="36" y="22" font-family="'Segoe UI', Arial, sans-serif" font-size="14" font-weight="600" fill="white">VM Deploy</text>
                </svg>
            </a>
            <ul class="navbar-nav">
                <li class="navbar-item">
                    <a href="#" class="navbar-link">帮助</a>
                </li>
                <li class="navbar-item">
                    <a href="#" class="navbar-link">设置</a>
                </li>
                <li class="navbar-item">
                    <a href="index.html" class="navbar-link">退出</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <ul class="sidebar-nav">
            <li class="sidebar-item">
                <a href="dashboard.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="3" y="3" width="7" height="7"></rect>
                        <rect x="14" y="3" width="7" height="7"></rect>
                        <rect x="14" y="14" width="7" height="7"></rect>
                        <rect x="3" y="14" width="7" height="7"></rect>
                    </svg>
                    仪表盘
                </a>
            </li>
            <li class="sidebar-item">
                <a href="templates.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10 9 9 9 8 9"></polyline>
                    </svg>
                    模板库
                </a>
            </li>
            <li class="sidebar-item">
                <a href="topology.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="2" y1="12" x2="22" y2="12"></line>
                        <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                    </svg>
                    拓扑设计器
                </a>
            </li>
            <li class="sidebar-item">
                <a href="deploy.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                        <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                        <line x1="12" y1="22.08" x2="12" y2="12"></line>
                    </svg>
                    部署中心
                </a>
            </li>
            <li class="sidebar-item">
                <a href="batch-deploy.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="9" y="2" width="6" height="6"></rect>
                        <rect x="16" y="16" width="6" height="6"></rect>
                        <rect x="2" y="16" width="6" height="6"></rect>
                        <path d="M5 16v-4h14v4"></path>
                        <path d="M12 12V8"></path>
                    </svg>
                    批量部署
                </a>
            </li>
            <li class="sidebar-item">
                <a href="machines.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                        <line x1="8" y1="21" x2="16" y2="21"></line>
                        <line x1="12" y1="17" x2="12" y2="21"></line>
                    </svg>
                    虚拟机管理
                </a>
            </li>
            <li class="sidebar-item">
                <a href="monitoring.html" class="sidebar-link active">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                    </svg>
                    资源监控
                </a>
            </li>
            <li class="sidebar-item">
                <a href="users.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                    用户管理
                </a>
            </li>
        </ul>
    </aside>

    <!-- 主内容区域 -->
    <main class="main">
        <div class="container">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                <h1>资源监控</h1>
                <div>
                    <select class="form-control" style="display: inline-block; width: auto; margin-right: 8px;">
                        <option value="1h">最近1小时</option>
                        <option value="6h">最近6小时</option>
                        <option value="24h" selected>最近24小时</option>
                        <option value="7d">最近7天</option>
                        <option value="30d">最近30天</option>
                    </select>
                    <button class="btn" style="background-color: var(--bg-light); border-color: var(--border-color);">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: text-bottom;">
                            <polyline points="23 4 23 10 17 10"></polyline>
                            <polyline points="1 20 1 14 7 14"></polyline>
                            <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                        </svg>
                        刷新
                    </button>
                </div>
            </div>

            <!-- 系统概览 -->
            <div class="dashboard-grid">
                <div class="stat-card">
                    <div class="stat-title">CPU使用率</div>
                    <div class="stat-value">68%</div>
                    <div class="stat-description">较昨日 <span style="color: var(--error-color);">+15%</span></div>
                    <!-- 模拟进度条 -->
                    <div style="margin-top: 16px; background-color: var(--bg-light); border-radius: 4px; height: 8px; overflow: hidden;">
                        <div style="width: 68%; height: 100%; background-color: var(--primary-color);"></div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">内存使用率</div>
                    <div class="stat-value">42%</div>
                    <div class="stat-description">较昨日 <span style="color: var(--success-color);">-8%</span></div>
                    <!-- 模拟进度条 -->
                    <div style="margin-top: 16px; background-color: var(--bg-light); border-radius: 4px; height: 8px; overflow: hidden;">
                        <div style="width: 42%; height: 100%; background-color: var(--success-color);"></div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">存储使用率</div>
                    <div class="stat-value">56%</div>
                    <div class="stat-description">较昨日 <span style="color: var(--warning-color);">+2%</span></div>
                    <!-- 模拟进度条 -->
                    <div style="margin-top: 16px; background-color: var(--bg-light); border-radius: 4px; height: 8px; overflow: hidden;">
                        <div style="width: 56%; height: 100%; background-color: var(--warning-color);"></div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-title">网络带宽</div>
                    <div class="stat-value">24 MB/s</div>
                    <div class="stat-description">较昨日 <span style="color: var(--error-color);">+5 MB/s</span></div>
                    <!-- 模拟进度条 -->
                    <div style="margin-top: 16px; background-color: var(--bg-light); border-radius: 4px; height: 8px; overflow: hidden;">
                        <div style="width: 48%; height: 100%; background-color: var(--primary-color);"></div>
                    </div>
                </div>
            </div>

            <!-- CPU使用率图表 -->
            <div class="card" style="margin-bottom: 24px;">
                <div class="card-header">
                    <h2 class="card-title">CPU使用率</h2>
                </div>
                <div class="card-body">
                    <!-- 模拟图表 - 在实际应用中应使用真实的图表库如Chart.js -->
                    <div style="height: 300px; position: relative;">
                        <!-- 模拟Y轴标签 -->
                        <div style="position: absolute; left: 0; top: 0; bottom: 0; width: 40px; display: flex; flex-direction: column; justify-content: space-between; align-items: flex-end; padding: 10px 5px;">
                            <span style="font-size: 12px; color: var(--text-light);">100%</span>
                            <span style="font-size: 12px; color: var(--text-light);">75%</span>
                            <span style="font-size: 12px; color: var(--text-light);">50%</span>
                            <span style="font-size: 12px; color: var(--text-light);">25%</span>
                            <span style="font-size: 12px; color: var(--text-light);">0%</span>
                        </div>
                        <!-- 模拟图表区域 -->
                        <div style="margin-left: 40px; height: 100%; background-color: var(--bg-light); border-radius: var(--radius); position: relative; overflow: hidden;">
                            <!-- 模拟网格线 -->
                            <div style="position: absolute; left: 0; right: 0; top: 25%; height: 1px; background-color: rgba(0,0,0,0.1);"></div>
                            <div style="position: absolute; left: 0; right: 0; top: 50%; height: 1px; background-color: rgba(0,0,0,0.1);"></div>
                            <div style="position: absolute; left: 0; right: 0; top: 75%; height: 1px; background-color: rgba(0,0,0,0.1);"></div>
                            <!-- 模拟数据线 - 在实际应用中应使用SVG或Canvas绘制 -->
                            <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none" style="position: absolute; left: 0; top: 0;">
                                <path d="M0,80 C10,70 20,85 30,60 C40,50 50,70 60,30 C70,40 80,20 90,40 L90,100 L0,100 Z" fill="rgba(26, 115, 232, 0.2)"></path>
                                <path d="M0,80 C10,70 20,85 30,60 C40,50 50,70 60,30 C70,40 80,20 90,40" fill="none" stroke="var(--primary-color)" stroke-width="2"></path>
                            </svg>
                            <!-- 模拟X轴标签 -->
                            <div style="position: absolute; left: 0; right: 0; bottom: 0; height: 20px; display: flex; justify-content: space-between; padding: 0 10px;">
                                <span style="font-size: 10px; color: var(--text-light);">00:00</span>
                                <span style="font-size: 10px; color: var(--text-light);">06:00</span>
                                <span style="font-size: 10px; color: var(--text-light);">12:00</span>
                                <span style="font-size: 10px; color: var(--text-light);">18:00</span>
                                <span style="font-size: 10px; color: var(--text-light);">24:00</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 内存使用率图表 -->
            <div class="card" style="margin-bottom: 24px;">
                <div class="card-header">
                    <h2 class="card-title">内存使用率</h2>
                </div>
                <div class="card-body">
                    <!-- 模拟图表 - 在实际应用中应使用真实的图表库如Chart.js -->
                    <div style="height: 300px; position: relative;">
                        <!-- 模拟Y轴标签 -->
                        <div style="position: absolute; left: 0; top: 0; bottom: 0; width: 40px; display: flex; flex-direction: column; justify-content: space-between; align-items: flex-end; padding: 10px 5px;">
                            <span style="font-size: 12px; color: var(--text-light);">100%</span>
                            <span style="font-size: 12px; color: var(--text-light);">75%</span>
                            <span style="font-size: 12px; color: var(--text-light);">50%</span>
                            <span style="font-size: 12px; color: var(--text-light);">25%</span>
                            <span style="font-size: 12px; color: var(--text-light);">0%</span>
                        </div>
                        <!-- 模拟图表区域 -->
                        <div style="margin-left: 40px; height: 100%; background-color: var(--bg-light); border-radius: var(--radius); position: relative; overflow: hidden;">
                            <!-- 模拟网格线 -->
                            <div style="position: absolute; left: 0; right: 0; top: 25%; height: 1px; background-color: rgba(0,0,0,0.1);"></div>
                            <div style="position: absolute; left: 0; right: 0; top: 50%; height: 1px; background-color: rgba(0,0,0,0.1);"></div>
                            <div style="position: absolute; left: 0; right: 0; top: 75%; height: 1px; background-color: rgba(0,0,0,0.1);"></div>
                            <!-- 模拟数据线 - 在实际应用中应使用SVG或Canvas绘制 -->
                            <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none" style="position: absolute; left: 0; top: 0;">
                                <path d="M0,60 C10,65 20,55 30,50 C40,45 50,50 60,40 C70,45 80,40 90,60 L90,100 L0,100 Z" fill="rgba(52, 168, 83, 0.2)"></path>
                                <path d="M0,60 C10,65 20,55 30,50 C40,45 50,50 60,40 C70,45 80,40 90,60" fill="none" stroke="var(--success-color)" stroke-width="2"></path>
                            </svg>
                            <!-- 模拟X轴标签 -->
                            <div style="position: absolute; left: 0; right: 0; bottom: 0; height: 20px; display: flex; justify-content: space-between; padding: 0 10px;">
                                <span style="font-size: 10px; color: var(--text-light);">00:00</span>
                                <span style="font-size: 10px; color: var(--text-light);">06:00</span>
                                <span style="font-size: 10px; color: var(--text-light);">12:00</span>
                                <span style="font-size: 10px; color: var(--text-light);">18:00</span>
                                <span style="font-size: 10px; color: var(--text-light);">24:00</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 虚拟机资源使用排行 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">虚拟机资源使用排行</h2>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>虚拟机名称</th>
                                    <th>CPU使用率</th>
                                    <th>内存使用率</th>
                                    <th>磁盘I/O</th>
                                    <th>网络I/O</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>db-server-01</td>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 100px; background-color: var(--bg-light); border-radius: 4px; height: 8px; margin-right: 8px; overflow: hidden;">
                                                <div style="width: 85%; height: 100%; background-color: var(--error-color);"></div>
                                            </div>
                                            <span>85%</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 100px; background-color: var(--bg-light); border-radius: 4px; height: 8px; margin-right: 8px; overflow: hidden;">
                                                <div style="width: 70%; height: 100%; background-color: var(--warning-color);"></div>
                                            </div>
                                            <span>70%</span>
                                        </div>
                                    </td>
                                    <td>12.5 MB/s</td>
                                    <td>8.2 MB/s</td>
                                </tr>
                                <tr>
                                    <td>web-server-01</td>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 100px; background-color: var(--bg-light); border-radius: 4px; height: 8px; margin-right: 8px; overflow: hidden;">
                                                <div style="width: 65%; height: 100%; background-color: var(--warning-color);"></div>
                                            </div>
                                            <span>65%</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 100px; background-color: var(--bg-light); border-radius: 4px; height: 8px; margin-right: 8px; overflow: hidden;">
                                                <div style="width: 45%; height: 100%; background-color: var(--primary-color);"></div>
                                            </div>
                                            <span>45%</span>
                                        </div>
                                    </td>
                                    <td>5.8 MB/s</td>
                                    <td>14.6 MB/s</td>
                                </tr>
                                <tr>
                                    <td>win10-desktop-01</td>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 100px; background-color: var(--bg-light); border-radius: 4px; height: 8px; margin-right: 8px; overflow: hidden;">
                                                <div style="width: 55%; height: 100%; background-color: var(--primary-color);"></div>
                                            </div>
                                            <span>55%</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 100px; background-color: var(--bg-light); border-radius: 4px; height: 8px; margin-right: 8px; overflow: hidden;">
                                                <div style="width: 60%; height: 100%; background-color: var(--warning-color);"></div>
                                            </div>
                                            <span>60%</span>
                                        </div>
                                    </td>
                                    <td>3.2 MB/s</td>
                                    <td>1.8 MB/s</td>
                                </tr>
                                <tr>
                                    <td>test-windows-01</td>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 100px; background-color: var(--bg-light); border-radius: 4px; height: 8px; margin-right: 8px; overflow: hidden;">
                                                <div style="width: 0%; height: 100%; background-color: var(--success-color);"></div>
                                            </div>
                                            <span>0%</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 100px; background-color: var(--bg-light); border-radius: 4px; height: 8px; margin-right: 8px; overflow: hidden;">
                                                <div style="width: 0%; height: 100%; background-color: var(--success-color);"></div>
                                            </div>
                                            <span>0%</span>
                                        </div>
                                    </td>
                                    <td>0 MB/s</td>
                                    <td>0 MB/s</td>
                                </tr>
                                <tr>
                                    <td>dev-env-01</td>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 100px; background-color: var(--bg-light); border-radius: 4px; height: 8px; margin-right: 8px; overflow: hidden;">
                                                <div style="width: 5%; height: 100%; background-color: var(--success-color);"></div>
                                            </div>
                                            <span>5%</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 100px; background-color: var(--bg-light); border-radius: 4px; height: 8px; margin-right: 8px; overflow: hidden;">
                                                <div style="width: 30%; height: 100%; background-color: var(--success-color);"></div>
                                            </div>
                                            <span>30%</span>
                                        </div>
                                    </td>
                                    <td>0.5 MB/s</td>
                                    <td>0.2 MB/s</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html> 