<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>环境还原 - 网络系统管理竞赛自动化运维平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f5f7fa;
            color: #2c3e50;
        }

        /* 导航栏 */
        .navbar {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            font-size: 20px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .navbar-user {
            display: flex;
            align-items: center;
            gap: 16px;
            color: #666;
            font-size: 14px;
        }

        .navbar-user a {
            color: #3182ce;
            text-decoration: none;
        }

        /* 侧边栏 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            bottom: 0;
            width: 240px;
            background: white;
            border-right: 1px solid #e2e8f0;
            padding: 24px 0;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 4px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.2s;
            font-size: 14px;
        }

        .sidebar-menu a:hover {
            background: #f7fafc;
            color: #1a1a1a;
        }

        .sidebar-menu a.active {
            background: #e6f2ff;
            color: #3182ce;
            font-weight: 500;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 240px;
            padding: 32px;
        }

        .page-header {
            margin-bottom: 32px;
        }

        .page-header h1 {
            font-size: 28px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 8px;
        }

        .page-header p {
            color: #666;
            font-size: 14px;
        }

        /* 快照管理 */
        .snapshots-section {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .section-header h2 {
            font-size: 20px;
            font-weight: 600;
            color: #1a1a1a;
        }

        /* 快照列表 */
        .snapshot-list {
            display: grid;
            gap: 16px;
        }

        .snapshot-item {
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            transition: all 0.2s;
            cursor: pointer;
        }

        .snapshot-item:hover {
            border-color: #3182ce;
            background: #f7fafc;
        }

        .snapshot-item.selected {
            border-color: #3182ce;
            background: #e6f2ff;
        }

        .snapshot-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .snapshot-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .snapshot-type {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .type-competition {
            background: #dbeafe;
            color: #3b82f6;
        }

        .type-training {
            background: #d1fae5;
            color: #10b981;
        }

        .type-custom {
            background: #fef3c7;
            color: #f59e0b;
        }

        .snapshot-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
        }

        .info-item {
            font-size: 14px;
            color: #666;
        }

        .info-item strong {
            color: #1a1a1a;
        }

        /* 还原选项 */
        .restore-options {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .option-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }

        .option-item {
            padding: 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .option-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .option-label {
            flex: 1;
        }

        .option-name {
            font-size: 14px;
            font-weight: 500;
            color: #1a1a1a;
        }

        .option-desc {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        /* 还原操作 */
        .restore-actions {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .action-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .btn {
            padding: 14px 32px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #1a1a1a;
            color: white;
        }

        .btn-primary:hover {
            background: #2d2d2d;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: white;
            color: #1a1a1a;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #f7fafc;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-large {
            padding: 18px 48px;
            font-size: 18px;
        }

        .warning-text {
            font-size: 14px;
            color: #ef4444;
            margin-top: 16px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="navbar-brand">网络系统管理竞赛自动化运维平台</div>
        <div class="navbar-user">
            <span>管理员</span>
            <a href="index.html">退出</a>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html">📊 仪表板</a></li>
            <li><a href="competition-env.html">🏆 竞赛环境管理</a></li>
            <li><a href="auto-deploy.html">🚀 一键部署</a></li>
            <li><a href="batch-workstation.html">💻 工位批量管理</a></li>
            <li><a href="scoring-system.html">📝 自动评分系统</a></li>
            <li><a href="fault-management.html">🔧 故障排除模块</a></li>
            <li><a href="module-management.html">📦 模块管理</a></li>
            <li><a href="environment-restore.html" class="active">🔄 环境还原</a></li>
            <li><a href="monitoring.html">📈 监控中心</a></li>
        </ul>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="page-header">
            <h1>环境还原</h1>
            <p>满足竞赛评分初始化环境要求的一键还原功能</p>
        </div>

        <!-- 快照管理 -->
        <div class="snapshots-section">
            <div class="section-header">
                <h2>环境快照</h2>
                <button class="btn btn-secondary" onclick="alert('功能演示：创建新快照')">
                    <span>📸</span>
                    <span>创建快照</span>
                </button>
            </div>
            <div class="snapshot-list">
                <div class="snapshot-item selected" onclick="selectSnapshot(this)">
                    <div class="snapshot-header">
                        <h3 class="snapshot-title">WSC2025 竞赛标准环境</h3>
                        <span class="snapshot-type type-competition">竞赛环境</span>
                    </div>
                    <div class="snapshot-info">
                        <div class="info-item">创建时间: <strong>2025-01-20</strong></div>
                        <div class="info-item">环境规模: <strong>200工位</strong></div>
                        <div class="info-item">包含模块: <strong>全部模块</strong></div>
                        <div class="info-item">快照大小: <strong>45.6 GB</strong></div>
                    </div>
                </div>

                <div class="snapshot-item" onclick="selectSnapshot(this)">
                    <div class="snapshot-header">
                        <h3 class="snapshot-title">基础训练环境</h3>
                        <span class="snapshot-type type-training">训练环境</span>
                    </div>
                    <div class="snapshot-info">
                        <div class="info-item">创建时间: <strong>2025-01-15</strong></div>
                        <div class="info-item">环境规模: <strong>50工位</strong></div>
                        <div class="info-item">包含模块: <strong>基础模块</strong></div>
                        <div class="info-item">快照大小: <strong>12.3 GB</strong></div>
                    </div>
                </div>

                <div class="snapshot-item" onclick="selectSnapshot(this)">
                    <div class="snapshot-header">
                        <h3 class="snapshot-title">自定义测试环境</h3>
                        <span class="snapshot-type type-custom">自定义</span>
                    </div>
                    <div class="snapshot-info">
                        <div class="info-item">创建时间: <strong>2025-01-10</strong></div>
                        <div class="info-item">环境规模: <strong>10工位</strong></div>
                        <div class="info-item">包含模块: <strong>选定模块</strong></div>
                        <div class="info-item">快照大小: <strong>3.2 GB</strong></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 还原选项 -->
        <div class="restore-options">
            <h2>还原选项</h2>
            <div class="option-grid">
                <div class="option-item">
                    <input type="checkbox" id="restore-config" checked>
                    <label for="restore-config" class="option-label">
                        <div class="option-name">系统配置</div>
                        <div class="option-desc">还原所有系统配置文件</div>
                    </label>
                </div>
                <div class="option-item">
                    <input type="checkbox" id="restore-network" checked>
                    <label for="restore-network" class="option-label">
                        <div class="option-name">网络配置</div>
                        <div class="option-desc">还原网络设置和路由表</div>
                    </label>
                </div>
                <div class="option-item">
                    <input type="checkbox" id="restore-services" checked>
                    <label for="restore-services" class="option-label">
                        <div class="option-name">服务状态</div>
                        <div class="option-desc">还原所有服务到初始状态</div>
                    </label>
                </div>
                <div class="option-item">
                    <input type="checkbox" id="restore-data">
                    <label for="restore-data" class="option-label">
                        <div class="option-name">用户数据</div>
                        <div class="option-desc">清除并还原用户数据</div>
                    </label>
                </div>
                <div class="option-item">
                    <input type="checkbox" id="restore-logs">
                    <label for="restore-logs" class="option-label">
                        <div class="option-name">日志文件</div>
                        <div class="option-desc">清理所有日志文件</div>
                    </label>
                </div>
                <div class="option-item">
                    <input type="checkbox" id="restore-faults">
                    <label for="restore-faults" class="option-label">
                        <div class="option-name">故障状态</div>
                        <div class="option-desc">重置所有故障项</div>
                    </label>
                </div>
            </div>
        </div>

        <!-- 还原操作 -->
        <div class="restore-actions">
            <div class="action-buttons">
                <button class="btn btn-danger btn-large" onclick="confirmRestore()">
                    <span>🔄</span>
                    <span>一键还原环境</span>
                </button>
                <button class="btn btn-secondary" onclick="alert('功能演示：预览还原内容')">
                    <span>👁️</span>
                    <span>预览</span>
                </button>
                <button class="btn btn-secondary" onclick="alert('功能演示：定时还原设置')">
                    <span>⏰</span>
                    <span>定时还原</span>
                </button>
            </div>
            <p class="warning-text">
                ⚠️ 警告：环境还原将覆盖当前所有配置，此操作不可撤销！
            </p>
        </div>
    </div>

    <script>
        function selectSnapshot(element) {
            // 移除所有选中状态
            document.querySelectorAll('.snapshot-item').forEach(item => {
                item.classList.remove('selected');
            });
            // 添加选中状态
            element.classList.add('selected');
        }

        function confirmRestore() {
            if (confirm('确定要还原到选定的环境快照吗？\n\n此操作将：\n- 覆盖当前所有系统配置\n- 重置所有服务状态\n- 清除指定的数据内容\n\n此操作不可撤销！')) {
                alert('功能演示：开始执行环境还原...\n\n预计需要5-10分钟完成\n请勿关闭页面或断开连接');
            }
        }
    </script>
</body>
</html>
