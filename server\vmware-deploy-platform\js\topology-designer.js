/**
 * VMware Workstation 拓扑设计器
 * 提供交互式网络拓扑设计功能
 */

// 画布设置
let canvas;
let ctx;
let canvasOffset;
let offsetX;
let offsetY;
let startX;
let startY;

// 拓扑元素
let nodes = [];
let connections = [];
let selectedNode = null;
let selectedConnection = null;
let draggingNode = false;
let creatingConnection = false;
let connectionStartNode = null;

// 组件库定义
const componentLibrary = [
    { id: 'windows-server', name: 'Windows Server', icon: 'windows-server.png', type: 'vm' },
    { id: 'linux-server', name: 'Linux Server', icon: 'linux-server.png', type: 'vm' },
    { id: 'router', name: '路由器', icon: 'router.png', type: 'network' },
    { id: 'switch', name: '交换机', icon: 'switch.png', type: 'network' },
    { id: 'firewall', name: '防火墙', icon: 'firewall.png', type: 'network' }
];

// 初始化拓扑设计器
function initTopologyDesigner() {
    canvas = document.getElementById('topology-canvas');
    ctx = canvas.getContext('2d');
    
    // 设置画布尺寸
    resizeCanvas();
    
    // 计算画布偏移
    updateCanvasOffset();
    
    // 注册事件监听器
    registerEventListeners();
    
    // 加载组件库
    loadComponentLibrary();
    
    // 初始绘制
    drawTopology();
}

// 调整画布尺寸
function resizeCanvas() {
    const container = document.querySelector('.topology-container');
    canvas.width = container.clientWidth;
    canvas.height = container.clientHeight;
}

// 更新画布偏移
function updateCanvasOffset() {
    canvasOffset = canvas.getBoundingClientRect();
    offsetX = canvasOffset.left;
    offsetY = canvasOffset.top;
}

// 注册事件监听器
function registerEventListeners() {
    // 窗口大小改变时重新调整画布
    window.addEventListener('resize', function() {
        resizeCanvas();
        updateCanvasOffset();
        drawTopology();
    });
    
    // 鼠标事件
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);
    
    // 组件库拖拽
    document.querySelectorAll('.component-item').forEach(item => {
        item.addEventListener('dragstart', handleDragStart);
    });
    
    canvas.addEventListener('dragover', handleDragOver);
    canvas.addEventListener('drop', handleDrop);
    
    // 工具栏按钮
    document.getElementById('save-topology').addEventListener('click', saveTopology);
    document.getElementById('load-topology').addEventListener('click', loadTopology);
    document.getElementById('validate-topology').addEventListener('click', validateTopology);
    document.getElementById('deploy-topology').addEventListener('click', deployTopology);
}

// 加载组件库
function loadComponentLibrary() {
    const libraryContainer = document.querySelector('.component-library');
    
    componentLibrary.forEach(component => {
        const componentElement = document.createElement('div');
        componentElement.className = 'component-item';
        componentElement.setAttribute('draggable', 'true');
        componentElement.setAttribute('data-component-id', component.id);
        
        componentElement.innerHTML = `
            <img src="images/${component.icon}" alt="${component.name}">
            <span>${component.name}</span>
        `;
        
        libraryContainer.appendChild(componentElement);
    });
}

// 绘制拓扑
function drawTopology() {
    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // 绘制网格背景
    drawGrid();
    
    // 绘制连接
    drawConnections();
    
    // 绘制节点
    drawNodes();
    
    // 绘制正在创建的连接
    if (creatingConnection) {
        drawCreatingConnection();
    }
}

// 绘制网格背景
function drawGrid() {
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 0.5;
    
    const gridSize = 20;
    
    for (let x = 0; x < canvas.width; x += gridSize) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, canvas.height);
        ctx.stroke();
    }
    
    for (let y = 0; y < canvas.height; y += gridSize) {
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
    }
}

// 绘制节点
function drawNodes() {
    nodes.forEach(node => {
        // 绘制节点图标
        const img = new Image();
        img.src = `images/${node.icon}`;
        ctx.drawImage(img, node.x - 25, node.y - 25, 50, 50);
        
        // 绘制节点名称
        ctx.font = '12px Arial';
        ctx.fillStyle = '#333';
        ctx.textAlign = 'center';
        ctx.fillText(node.name, node.x, node.y + 40);
        
        // 如果节点被选中，绘制选中效果
        if (selectedNode === node) {
            ctx.strokeStyle = '#3498db';
            ctx.lineWidth = 2;
            ctx.strokeRect(node.x - 30, node.y - 30, 60, 60);
        }
    });
}

// 绘制连接
function drawConnections() {
    connections.forEach(conn => {
        const sourceNode = nodes.find(n => n.id === conn.sourceId);
        const targetNode = nodes.find(n => n.id === conn.targetId);
        
        if (sourceNode && targetNode) {
            ctx.beginPath();
            ctx.moveTo(sourceNode.x, sourceNode.y);
            ctx.lineTo(targetNode.x, targetNode.y);
            
            // 根据连接类型设置样式
            switch(conn.type) {
                case 'lan':
                    ctx.strokeStyle = '#2ecc71';
                    break;
                case 'wan':
                    ctx.strokeStyle = '#e74c3c';
                    break;
                default:
                    ctx.strokeStyle = '#3498db';
            }
            
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 如果连接被选中，绘制选中效果
            if (selectedConnection === conn) {
                ctx.strokeStyle = '#9b59b6';
                ctx.lineWidth = 4;
                ctx.stroke();
            }
            
            // 绘制连接标签
            if (conn.label) {
                const midX = (sourceNode.x + targetNode.x) / 2;
                const midY = (sourceNode.y + targetNode.y) / 2;
                
                ctx.font = '11px Arial';
                ctx.fillStyle = '#333';
                ctx.textAlign = 'center';
                ctx.fillText(conn.label, midX, midY - 5);
            }
        }
    });
}

// 绘制正在创建的连接
function drawCreatingConnection() {
    if (connectionStartNode) {
        ctx.beginPath();
        ctx.moveTo(connectionStartNode.x, connectionStartNode.y);
        ctx.lineTo(startX, startY);
        ctx.strokeStyle = '#3498db';
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 3]);
        ctx.stroke();
        ctx.setLineDash([]);
    }
}

// 鼠标按下处理
function handleMouseDown(e) {
    e.preventDefault();
    startX = parseInt(e.clientX - offsetX);
    startY = parseInt(e.clientY - offsetY);
    
    // 检查是否点击了节点
    const clickedNode = findNodeAt(startX, startY);
    if (clickedNode) {
        selectedNode = clickedNode;
        selectedConnection = null;
        
        // 检查是否按下了Ctrl键（创建连接）
        if (e.ctrlKey) {
            creatingConnection = true;
            connectionStartNode = clickedNode;
        } else {
            draggingNode = true;
        }
    } else {
        // 检查是否点击了连接
        const clickedConnection = findConnectionAt(startX, startY);
        if (clickedConnection) {
            selectedNode = null;
            selectedConnection = clickedConnection;
        } else {
            selectedNode = null;
            selectedConnection = null;
        }
    }
    
    // 更新属性面板
    updatePropertiesPanel();
    
    // 重绘拓扑
    drawTopology();
}

// 鼠标移动处理
function handleMouseMove(e) {
    e.preventDefault();
    const mouseX = parseInt(e.clientX - offsetX);
    const mouseY = parseInt(e.clientY - offsetY);
    
    if (draggingNode && selectedNode) {
        // 移动节点
        selectedNode.x = mouseX;
        selectedNode.y = mouseY;
        drawTopology();
    } else if (creatingConnection) {
        // 更新正在创建的连接终点
        startX = mouseX;
        startY = mouseY;
        drawTopology();
    }
}

// 鼠标释放处理
function handleMouseUp(e) {
    e.preventDefault();
    
    if (creatingConnection) {
        const mouseX = parseInt(e.clientX - offsetX);
        const mouseY = parseInt(e.clientY - offsetY);
        
        // 检查是否释放在另一个节点上
        const targetNode = findNodeAt(mouseX, mouseY);
        if (targetNode && targetNode !== connectionStartNode) {
            // 创建新连接
            createConnection(connectionStartNode.id, targetNode.id);
        }
        
        creatingConnection = false;
        connectionStartNode = null;
    }
    
    draggingNode = false;
    drawTopology();
}

// 查找指定位置的节点
function findNodeAt(x, y) {
    // 从后向前检查（后添加的节点在上层）
    for (let i = nodes.length - 1; i >= 0; i--) {
        const node = nodes[i];
        const dx = node.x - x;
        const dy = node.y - y;
        
        // 简单的圆形碰撞检测
        if (dx * dx + dy * dy < 30 * 30) {
            return node;
        }
    }
    return null;
}

// 查找指定位置的连接
function findConnectionAt(x, y) {
    const threshold = 5; // 点击误差范围
    
    for (let i = 0; i < connections.length; i++) {
        const conn = connections[i];
        const sourceNode = nodes.find(n => n.id === conn.sourceId);
        const targetNode = nodes.find(n => n.id === conn.targetId);
        
        if (sourceNode && targetNode) {
            // 计算点到线段的距离
            const distance = pointToLineDistance(
                x, y,
                sourceNode.x, sourceNode.y,
                targetNode.x, targetNode.y
            );
            
            if (distance < threshold) {
                return conn;
            }
        }
    }
    return null;
}

// 计算点到线段的距离
function pointToLineDistance(px, py, x1, y1, x2, y2) {
    const A = px - x1;
    const B = py - y1;
    const C = x2 - x1;
    const D = y2 - y1;
    
    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    let param = -1;
    
    if (lenSq !== 0) {
        param = dot / lenSq;
    }
    
    let xx, yy;
    
    if (param < 0) {
        xx = x1;
        yy = y1;
    } else if (param > 1) {
        xx = x2;
        yy = y2;
    } else {
        xx = x1 + param * C;
        yy = y1 + param * D;
    }
    
    const dx = px - xx;
    const dy = py - yy;
    
    return Math.sqrt(dx * dx + dy * dy);
}

// 创建连接
function createConnection(sourceId, targetId) {
    const newConnection = {
        id: 'conn-' + Date.now(),
        sourceId: sourceId,
        targetId: targetId,
        type: 'lan',
        label: ''
    };
    
    connections.push(newConnection);
    selectedConnection = newConnection;
    updatePropertiesPanel();
}

// 处理拖拽开始
function handleDragStart(e) {
    const componentId = e.target.getAttribute('data-component-id');
    e.dataTransfer.setData('text/plain', componentId);
}

// 处理拖拽经过
function handleDragOver(e) {
    e.preventDefault();
}

// 处理拖拽放置
function handleDrop(e) {
    e.preventDefault();
    const componentId = e.dataTransfer.getData('text/plain');
    const component = componentLibrary.find(c => c.id === componentId);
    
    if (component) {
        const x = parseInt(e.clientX - offsetX);
        const y = parseInt(e.clientY - offsetY);
        
        // 创建新节点
        const newNode = {
            id: component.id + '-' + Date.now(),
            name: component.name,
            type: component.type,
            icon: component.icon,
            x: x,
            y: y,
            properties: {
                cpu: 1,
                memory: 1024,
                disk: 20,
                os: component.id.includes('windows') ? 'Windows' : 'Linux'
            }
        };
        
        nodes.push(newNode);
        selectedNode = newNode;
        selectedConnection = null;
        updatePropertiesPanel();
        drawTopology();
    }
}

// 更新属性面板
function updatePropertiesPanel() {
    const propertiesPanel = document.querySelector('.properties-panel');
    
    if (selectedNode) {
        // 显示节点属性
        propertiesPanel.innerHTML = `
            <h3>节点属性</h3>
            <div class="property-group">
                <label for="node-name">名称:</label>
                <input type="text" id="node-name" value="${selectedNode.name}" onchange="updateNodeProperty('name', this.value)">
            </div>
            <div class="property-group">
                <label for="node-type">类型:</label>
                <input type="text" id="node-type" value="${selectedNode.type}" disabled>
            </div>
            ${selectedNode.type === 'vm' ? `
                <div class="property-group">
                    <label for="node-cpu">CPU (核):</label>
                    <input type="number" id="node-cpu" value="${selectedNode.properties.cpu}" min="1" max="16" onchange="updateNodeProperty('cpu', this.value)">
                </div>
                <div class="property-group">
                    <label for="node-memory">内存 (MB):</label>
                    <input type="number" id="node-memory" value="${selectedNode.properties.memory}" min="512" step="512" onchange="updateNodeProperty('memory', this.value)">
                </div>
                <div class="property-group">
                    <label for="node-disk">磁盘 (GB):</label>
                    <input type="number" id="node-disk" value="${selectedNode.properties.disk}" min="10" onchange="updateNodeProperty('disk', this.value)">
                </div>
                <div class="property-group">
                    <label for="node-os">操作系统:</label>
                    <select id="node-os" onchange="updateNodeProperty('os', this.value)">
                        <option value="Windows" ${selectedNode.properties.os === 'Windows' ? 'selected' : ''}>Windows</option>
                        <option value="Linux" ${selectedNode.properties.os === 'Linux' ? 'selected' : ''}>Linux</option>
                    </select>
                </div>
            ` : ''}
            <div class="property-actions">
                <button onclick="deleteSelectedNode()">删除节点</button>
            </div>
        `;
    } else if (selectedConnection) {
        // 显示连接属性
        propertiesPanel.innerHTML = `
            <h3>连接属性</h3>
            <div class="property-group">
                <label for="conn-type">类型:</label>
                <select id="conn-type" onchange="updateConnectionProperty('type', this.value)">
                    <option value="lan" ${selectedConnection.type === 'lan' ? 'selected' : ''}>局域网 (LAN)</option>
                    <option value="wan" ${selectedConnection.type === 'wan' ? 'selected' : ''}>广域网 (WAN)</option>
                </select>
            </div>
            <div class="property-group">
                <label for="conn-label">标签:</label>
                <input type="text" id="conn-label" value="${selectedConnection.label}" onchange="updateConnectionProperty('label', this.value)">
            </div>
            <div class="property-actions">
                <button onclick="deleteSelectedConnection()">删除连接</button>
            </div>
        `;
    } else {
        // 没有选中任何对象
        propertiesPanel.innerHTML = '<p>选择一个节点或连接以查看属性</p>';
    }
}

// 更新节点属性
function updateNodeProperty(property, value) {
    if (selectedNode) {
        if (['cpu', 'memory', 'disk'].includes(property)) {
            selectedNode.properties[property] = parseInt(value);
        } else if (property === 'os') {
            selectedNode.properties[property] = value;
        } else {
            selectedNode[property] = value;
        }
        drawTopology();
    }
}

// 更新连接属性
function updateConnectionProperty(property, value) {
    if (selectedConnection) {
        selectedConnection[property] = value;
        drawTopology();
    }
}

// 删除选中的节点
function deleteSelectedNode() {
    if (selectedNode) {
        // 删除与该节点相关的所有连接
        connections = connections.filter(conn => 
            conn.sourceId !== selectedNode.id && conn.targetId !== selectedNode.id
        );
        
        // 删除节点
        nodes = nodes.filter(node => node !== selectedNode);
        
        selectedNode = null;
        updatePropertiesPanel();
        drawTopology();
    }
}

// 删除选中的连接
function deleteSelectedConnection() {
    if (selectedConnection) {
        connections = connections.filter(conn => conn !== selectedConnection);
        selectedConnection = null;
        updatePropertiesPanel();
        drawTopology();
    }
}

// 保存拓扑
function saveTopology() {
    const topologyData = {
        nodes: nodes,
        connections: connections
    };
    
    const dataStr = JSON.stringify(topologyData);
    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);
    
    const exportName = 'topology-' + new Date().toISOString().slice(0, 10) + '.json';
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportName);
    linkElement.click();
}

// 加载拓扑
function loadTopology() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = e => {
        const file = e.target.files[0];
        const reader = new FileReader();
        
        reader.onload = event => {
            try {
                const topologyData = JSON.parse(event.target.result);
                
                if (topologyData.nodes && topologyData.connections) {
                    nodes = topologyData.nodes;
                    connections = topologyData.connections;
                    
                    selectedNode = null;
                    selectedConnection = null;
                    updatePropertiesPanel();
                    drawTopology();
                }
            } catch (error) {
                alert('无法加载拓扑文件: ' + error.message);
            }
        };
        
        reader.readAsText(file);
    };
    
    input.click();
}

// 验证拓扑
function validateTopology() {
    let isValid = true;
    let messages = [];
    
    // 检查是否有节点
    if (nodes.length === 0) {
        isValid = false;
        messages.push('拓扑必须包含至少一个节点');
    }
    
    // 检查是否有未连接的节点
    const connectedNodeIds = new Set();
    connections.forEach(conn => {
        connectedNodeIds.add(conn.sourceId);
        connectedNodeIds.add(conn.targetId);
    });
    
    const isolatedNodes = nodes.filter(node => !connectedNodeIds.has(node.id));
    if (isolatedNodes.length > 0 && nodes.length > 1) {
        isValid = false;
        messages.push(`发现 ${isolatedNodes.length} 个未连接的节点`);
    }
    
    // 显示验证结果
    alert(isValid ? '拓扑验证通过！' : '拓扑验证失败:\n' + messages.join('\n'));
    
    return isValid;
}

// 部署拓扑
function deployTopology() {
    if (validateTopology()) {
        // 准备部署数据
        const deployData = {
            name: document.getElementById('topology-name').value || '未命名拓扑',
            description: document.getElementById('topology-description').value || '',
            nodes: nodes,
            connections: connections
        };
        
        // 将数据存储在本地存储中（在实际应用中，这里应该是发送到服务器）
        localStorage.setItem('pendingTopologyDeploy', JSON.stringify(deployData));
        
        // 跳转到批量部署页面
        window.location.href = 'batch-deploy.html';
    }
}

// 页面加载时初始化
window.addEventListener('load', initTopologyDesigner); 