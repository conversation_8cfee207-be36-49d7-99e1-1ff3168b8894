<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>批量部署 - VMware Workstation 虚拟机自动部署平台</title>
  <link rel="stylesheet" href="css/normalize.css">
  <link rel="stylesheet" href="css/style.css">
</head>
<body>
  <!-- 导航栏 -->
  <nav class="navbar">
    <div class="navbar-container container">
      <a href="dashboard.html" class="navbar-brand">
        <img src="images/logo.png" alt="Logo">
        VMware 虚拟机自动部署平台
      </a>
      <ul class="navbar-nav">
        <li class="navbar-item">
          <a href="#" class="navbar-link">通知</a>
        </li>
        <li class="navbar-item">
          <a href="#" class="navbar-link">帮助</a>
        </li>
        <li class="navbar-item">
          <a href="index.html" class="navbar-link">退出</a>
        </li>
      </ul>
    </div>
  </nav>

  <!-- 侧边栏 -->
  <aside class="sidebar">
    <ul class="sidebar-nav">
      <li class="sidebar-item">
        <a href="dashboard.html" class="sidebar-link">
          <span class="sidebar-icon">📊</span>
          仪表盘
        </a>
      </li>
      <li class="sidebar-item">
        <a href="templates.html" class="sidebar-link">
          <span class="sidebar-icon">📁</span>
          模板库
        </a>
      </li>
      <li class="sidebar-item">
        <a href="topology.html" class="sidebar-link">
          <span class="sidebar-icon">🔄</span>
          拓扑设计器
        </a>
      </li>
      <li class="sidebar-item">
        <a href="deploy.html" class="sidebar-link">
          <span class="sidebar-icon">🚀</span>
          部署中心
        </a>
      </li>
      <li class="sidebar-item">
        <a href="batch-deploy.html" class="sidebar-link active">
          <span class="sidebar-icon">📦</span>
          批量部署
        </a>
      </li>
      <li class="sidebar-item">
        <a href="machines.html" class="sidebar-link">
          <span class="sidebar-icon">💻</span>
          虚拟机管理
        </a>
      </li>
      <li class="sidebar-item">
        <a href="monitoring.html" class="sidebar-link">
          <span class="sidebar-icon">📈</span>
          资源监控
        </a>
      </li>
      <li class="sidebar-item">
        <a href="users.html" class="sidebar-link">
          <span class="sidebar-icon">👥</span>
          用户管理
        </a>
      </li>
    </ul>
  </aside>

  <!-- 主内容区域 -->
  <main class="main">
    <div class="container">
      <h1>批量部署</h1>
      <p>将虚拟机拓扑同时部署到多台客户端机器。</p>

      <!-- 待部署拓扑信息 -->
      <div id="pending-topology-container" class="pending-topology" style="display: none;">
        <div class="pending-topology-header">
          <div class="pending-topology-title">待部署拓扑: <span id="pending-topology-name">未命名拓扑</span></div>
          <div>
            <button id="cancel-topology-btn" class="btn">取消</button>
          </div>
        </div>
        <div>
          <span>包含 <span id="pending-topology-nodes">0</span> 个节点和 <span id="pending-topology-connections">0</span> 个连接</span>
        </div>
        <div class="topology-preview-container">
          <canvas id="topology-preview" width="400" height="200"></canvas>
        </div>
      </div>

      <!-- 批量部署内容 -->
      <div class="batch-deploy-container">
        <!-- 客户端选择 -->
        <div class="clients-container card">
          <div class="card-header">
            <h2 class="card-title">选择客户端</h2>
          </div>
          <div class="card-body">
            <!-- 客户端过滤 -->
            <div class="client-filter">
              <input type="text" id="client-filter" class="form-control" placeholder="搜索客户端..." style="flex: 1;">
              <select id="client-group-filter" class="form-control">
                <option value="all">所有客户端</option>
                <option value="online">仅在线</option>
                <option value="offline">仅离线</option>
              </select>
              <button id="select-all-clients" class="btn">全选</button>
            </div>

            <!-- 客户端列表 -->
            <div id="client-list" class="client-list">
              <!-- 客户端将由JavaScript动态加载 -->
            </div>

            <!-- 部署按钮 -->
            <div style="margin-top: 16px; text-align: right;">
              <button id="deploy-topology-btn" class="btn btn-primary" disabled>部署到选中的客户端</button>
            </div>
          </div>
        </div>

        <!-- 部署任务 -->
        <div class="tasks-container card">
          <div class="card-header">
            <h2 class="card-title">部署任务</h2>
          </div>
          <div class="card-body">
            <div id="deployment-tasks" class="deployment-tasks">
              <!-- 任务将由JavaScript动态加载 -->
            </div>
          </div>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="card" style="margin-top: 24px;">
        <div class="card-header">
          <h2 class="card-title">批量部署说明</h2>
        </div>
        <div class="card-body">
          <h3>操作流程</h3>
          <ol>
            <li>在<a href="topology.html">拓扑设计器</a>中设计您的网络拓扑</li>
            <li>点击"部署拓扑"按钮将拓扑提交到批量部署页面</li>
            <li>在本页面选择要部署的客户端机器</li>
            <li>点击"部署到选中的客户端"按钮开始部署</li>
            <li>在部署任务区域监控部署进度</li>
          </ol>

          <h3>注意事项</h3>
          <ul>
            <li>只有在线的客户端才能接收部署任务</li>
            <li>确保客户端机器有足够的资源来运行您的虚拟机</li>
            <li>部署过程中请勿关闭客户端机器</li>
            <li>您可以随时取消正在进行的部署任务</li>
          </ul>
        </div>
      </div>
    </div>
  </main>

  <script src="js/batch-deploy.js"></script>
</body>
</html> 