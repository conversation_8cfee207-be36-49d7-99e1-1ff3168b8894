/* 主样式表 - VMware Workstation 虚拟机自动部署平台 */

/* 全局样式 */
:root {
  --primary-color: #1a73e8;
  --primary-dark: #0d47a1;
  --primary-light: #e8f0fe;
  --success-color: #34a853;
  --warning-color: #fbbc05;
  --error-color: #ea4335;
  --text-dark: #202124;
  --text-light: #5f6368;
  --bg-light: #f8f9fa;
  --bg-white: #ffffff;
  --border-color: #dadce0;
  --shadow-sm: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
  --shadow-md: 0 2px 6px 2px rgba(60, 64, 67, 0.15);
  --radius: 4px;
}

* {
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-dark);
  background-color: var(--bg-light);
  margin: 0;
  padding: 0;
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--text-dark);
  font-weight: 500;
  margin-top: 0;
}

/* 布局 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
}

.col {
  flex: 1;
  padding: 0 8px;
}

/* 登录页面 */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--bg-light);
}

.login-card {
  width: 100%;
  max-width: 400px;
  background-color: var(--bg-white);
  border-radius: var(--radius);
  box-shadow: var(--shadow-md);
  padding: 40px;
}

.login-logo {
  text-align: center;
  margin-bottom: 24px;
}

.login-logo img {
  height: 60px;
}

.login-title {
  text-align: center;
  font-size: 24px;
  margin-bottom: 24px;
}

/* 表单样式 */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-control {
  display: block;
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-dark);
  background-color: var(--bg-white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  transition: border-color 0.15s ease-in-out;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: 0;
}

/* 按钮样式 */
.btn {
  display: inline-block;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 8px 16px;
  font-size: 14px;
  line-height: 1.5;
  border-radius: var(--radius);
  transition: all 0.15s ease-in-out;
  cursor: pointer;
}

.btn-primary {
  color: white;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-block {
  display: block;
  width: 100%;
}

/* 导航栏 */
.navbar {
  background-color: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-sm);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.navbar-brand {
  display: flex;
  align-items: center;
  color: white;
  font-size: 20px;
  font-weight: 500;
}

.navbar-brand img {
  height: 32px;
  margin-right: 8px;
}

.navbar-nav {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

.navbar-item {
  margin-left: 24px;
}

.navbar-link {
  color: white;
  text-decoration: none;
}

/* 侧边栏 */
.sidebar {
  position: fixed;
  top: 64px;
  left: 0;
  bottom: 0;
  width: 240px;
  background-color: var(--bg-white);
  box-shadow: var(--shadow-sm);
  z-index: 900;
  overflow-y: auto;
}

.sidebar-nav {
  list-style: none;
  margin: 0;
  padding: 16px 0;
}

.sidebar-item {
  margin: 0;
  padding: 0;
}

.sidebar-link {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  color: var(--text-light);
  text-decoration: none;
  transition: all 0.15s ease-in-out;
}

.sidebar-link:hover {
  background-color: var(--primary-light);
  color: var(--primary-color);
  text-decoration: none;
}

.sidebar-link.active {
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-left: 4px solid var(--primary-color);
}

.sidebar-icon {
  margin-right: 12px;
  width: 20px;
  height: 20px;
}

/* 主内容区域 */
.main {
  margin-left: 240px;
  margin-top: 64px;
  padding: 24px;
  min-height: calc(100vh - 64px);
}

/* 卡片 */
.card {
  background-color: var(--bg-white);
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
  margin-bottom: 24px;
}

.card-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
}

.card-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.card-body {
  padding: 24px;
}

/* 仪表盘卡片网格 */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.stat-card {
  background-color: var(--bg-white);
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
  padding: 24px;
}

.stat-title {
  font-size: 14px;
  color: var(--text-light);
  margin: 0 0 8px 0;
}

.stat-value {
  font-size: 32px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.stat-description {
  font-size: 12px;
  color: var(--text-light);
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  font-weight: 500;
  color: var(--text-light);
  background-color: var(--bg-light);
}

.table tr:hover {
  background-color: var(--primary-light);
}

/* 状态标签 */
.status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-running {
  background-color: rgba(52, 168, 83, 0.1);
  color: var(--success-color);
}

.status-stopped {
  background-color: rgba(234, 67, 53, 0.1);
  color: var(--error-color);
}

.status-paused {
  background-color: rgba(251, 188, 5, 0.1);
  color: var(--warning-color);
}

/* 表单卡片 */
.form-card {
  max-width: 800px;
  margin: 0 auto;
}

/* 拓扑设计器样式 */
.topology-container {
  display: flex;
  height: calc(100vh - 160px);
  min-height: 600px;
  background-color: var(--bg-white);
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.component-sidebar {
  width: 240px;
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
}

.component-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  font-weight: 500;
}

.component-library {
  padding: 16px;
}

.component-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 8px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  cursor: move;
  transition: all 0.15s ease-in-out;
}

.component-item:hover {
  background-color: var(--primary-light);
  border-color: var(--primary-color);
}

.component-item img {
  width: 32px;
  height: 32px;
  margin-right: 12px;
}

.topology-canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.topology-toolbar {
  padding: 8px 16px;
  background-color: var(--bg-light);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.toolbar-group {
  display: flex;
  align-items: center;
}

.toolbar-item {
  margin-right: 12px;
}

.topology-info {
  display: flex;
  align-items: center;
}

.topology-info-item {
  margin-left: 16px;
  font-size: 12px;
  color: var(--text-light);
}

#topology-canvas {
  width: 100%;
  height: calc(100% - 50px);
  background-color: white;
}

.properties-panel {
  width: 280px;
  border-left: 1px solid var(--border-color);
  padding: 16px;
  overflow-y: auto;
}

.property-group {
  margin-bottom: 16px;
}

.property-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  font-size: 12px;
  color: var(--text-light);
}

.property-actions {
  margin-top: 24px;
}

.topology-metadata {
  margin-bottom: 24px;
}

.topology-metadata .form-group {
  margin-bottom: 12px;
}

/* 批量部署样式 */
.batch-deploy-container {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.clients-container {
  flex: 1;
  min-width: 300px;
}

.tasks-container {
  flex: 1;
  min-width: 300px;
}

.client-filter {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;
}

.client-list {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
}

.client-item {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.15s ease-in-out;
}

.client-item:last-child {
  border-bottom: none;
}

.client-item:hover {
  background-color: var(--primary-light);
}

.client-item.offline {
  opacity: 0.6;
}

.client-select {
  display: flex;
  align-items: center;
  padding-right: 12px;
}

.client-info {
  flex: 1;
}

.client-name {
  font-weight: 500;
}

.client-ip {
  font-size: 12px;
  color: var(--text-light);
}

.client-status {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-top: 4px;
  display: inline-block;
}

.client-status.online {
  background-color: rgba(52, 168, 83, 0.1);
  color: var(--success-color);
}

.client-status.offline {
  background-color: rgba(234, 67, 53, 0.1);
  color: var(--error-color);
}

.client-resources {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 100px;
}

.resource-item {
  font-size: 12px;
  color: var(--text-light);
  margin-bottom: 4px;
}

.resource-icon {
  margin-right: 4px;
}

.pending-topology {
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--primary-light);
  border-left: 4px solid var(--primary-color);
  border-radius: var(--radius);
}

.pending-topology-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.pending-topology-title {
  font-weight: 500;
  color: var(--primary-color);
}

.topology-preview-container {
  display: flex;
  justify-content: center;
  margin: 16px 0;
}

#topology-preview {
  border: 1px solid var(--border-color);
  background-color: white;
  border-radius: var(--radius);
}

.deployment-tasks {
  max-height: 600px;
  overflow-y: auto;
}

.task-item {
  background-color: var(--bg-white);
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
  margin-bottom: 16px;
  overflow: hidden;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--bg-light);
  border-bottom: 1px solid var(--border-color);
}

.task-name {
  font-weight: 500;
}

.task-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.task-status.completed {
  background-color: rgba(52, 168, 83, 0.1);
  color: var(--success-color);
}

.task-status.in_progress {
  background-color: rgba(251, 188, 5, 0.1);
  color: var(--warning-color);
}

.task-status.failed {
  background-color: rgba(234, 67, 53, 0.1);
  color: var(--error-color);
}

.task-details {
  padding: 16px;
}

.task-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 12px;
  color: var(--text-light);
}

.task-progress {
  margin-bottom: 12px;
}

.progress-bar {
  height: 8px;
  background-color: var(--bg-light);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-success {
  height: 100%;
  background-color: var(--success-color);
  float: left;
}

.progress-failed {
  height: 100%;
  background-color: var(--error-color);
  float: left;
}

.progress-text {
  font-size: 12px;
  text-align: right;
}

.task-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 12px;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .sidebar {
    width: 200px;
  }
  
  .main {
    margin-left: 200px;
  }
  
  .topology-container {
    flex-direction: column;
    height: auto;
  }
  
  .component-sidebar {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
  
  .properties-panel {
    width: 100%;
    border-left: none;
    border-top: 1px solid var(--border-color);
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }
  
  .sidebar.active {
    transform: translateX(0);
  }
  
  .main {
    margin-left: 0;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
} 