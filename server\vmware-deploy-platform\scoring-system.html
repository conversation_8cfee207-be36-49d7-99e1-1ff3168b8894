<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动评分系统 - 网络系统管理竞赛自动化运维平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f5f7fa;
            color: #2c3e50;
        }

        /* 导航栏 */
        .navbar {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            font-size: 20px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .navbar-user {
            display: flex;
            align-items: center;
            gap: 16px;
            color: #666;
            font-size: 14px;
        }

        .navbar-user a {
            color: #3182ce;
            text-decoration: none;
        }

        /* 侧边栏 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            bottom: 0;
            width: 240px;
            background: white;
            border-right: 1px solid #e2e8f0;
            padding: 24px 0;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 4px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.2s;
            font-size: 14px;
        }

        .sidebar-menu a:hover {
            background: #f7fafc;
            color: #1a1a1a;
        }

        .sidebar-menu a.active {
            background: #e6f2ff;
            color: #3182ce;
            font-weight: 500;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 240px;
            padding: 32px;
        }

        .page-header {
            margin-bottom: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-header h1 {
            font-size: 28px;
            font-weight: 600;
            color: #1a1a1a;
        }

        /* 评分概览 */
        .scoring-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .overview-card {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s;
        }

        .overview-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .overview-card h3 {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .overview-card .value {
            font-size: 32px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .overview-card .detail {
            font-size: 12px;
            color: #999;
            margin-top: 8px;
        }

        /* 评分任务 */
        .scoring-tasks {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .section-header h2 {
            font-size: 20px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #1a1a1a;
            color: white;
        }

        .btn-primary:hover {
            background: #2d2d2d;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: white;
            color: #1a1a1a;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #f7fafc;
        }

        /* 任务表格 */
        .task-table {
            width: 100%;
            border-collapse: collapse;
        }

        .task-table th {
            text-align: left;
            padding: 12px;
            background: #f7fafc;
            font-size: 14px;
            font-weight: 500;
            color: #4a5568;
            border-bottom: 1px solid #e2e8f0;
        }

        .task-table td {
            padding: 12px;
            border-bottom: 1px solid #f1f5f9;
            font-size: 14px;
        }

        .task-table tr:hover {
            background: #f7fafc;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-running {
            background: #dbeafe;
            color: #3b82f6;
        }

        .status-completed {
            background: #d1fae5;
            color: #10b981;
        }

        .status-pending {
            background: #fee2e2;
            color: #ef4444;
        }

        /* 评分配置 */
        .scoring-config {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .config-item {
            padding: 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            transition: all 0.2s;
        }

        .config-item:hover {
            border-color: #3182ce;
        }

        .config-item h4 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #1a1a1a;
        }

        .config-item p {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
        }

        .config-actions {
            display: flex;
            gap: 8px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="navbar-brand">网络系统管理竞赛自动化运维平台</div>
        <div class="navbar-user">
            <span>管理员</span>
            <a href="index.html">退出</a>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html">📊 仪表板</a></li>
            <li><a href="competition-env.html">🏆 竞赛环境管理</a></li>
            <li><a href="auto-deploy.html">🚀 一键部署</a></li>
            <li><a href="batch-workstation.html">💻 工位批量管理</a></li>
            <li><a href="scoring-system.html" class="active">📝 自动评分系统</a></li>
            <li><a href="fault-management.html">🔧 故障排除模块</a></li>
            <li><a href="module-management.html">📦 模块管理</a></li>
            <li><a href="environment-restore.html">🔄 环境还原</a></li>
            <li><a href="monitoring.html">📈 监控中心</a></li>
        </ul>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="page-header">
            <h1>自动评分系统</h1>
            <button class="btn btn-primary" onclick="alert('功能演示：创建新的评分任务')">
                <span>➕</span>
                <span>新建评分任务</span>
            </button>
        </div>

        <!-- 评分概览 -->
        <div class="scoring-overview">
            <div class="overview-card">
                <h3>今日评分任务</h3>
                <div class="value">89</div>
                <div class="detail">已完成 82 | 进行中 7</div>
            </div>
            <div class="overview-card">
                <h3>平均评分时间</h3>
                <div class="value">3.2</div>
                <div class="detail">分钟/工位</div>
            </div>
            <div class="overview-card">
                <h3>评分准确率</h3>
                <div class="value">99.8%</div>
                <div class="detail">基于人工复核</div>
            </div>
            <div class="overview-card">
                <h3>支持模块数</h3>
                <div class="value">12</div>
                <div class="detail">涵盖所有竞赛模块</div>
            </div>
        </div>

        <!-- 评分任务列表 -->
        <div class="scoring-tasks">
            <div class="section-header">
                <h2>评分任务列表</h2>
                <div>
                    <button class="btn btn-secondary" onclick="alert('功能演示：刷新任务列表')">
                        <span>🔄</span>
                        <span>刷新</span>
                    </button>
                </div>
            </div>
            <table class="task-table">
                <thead>
                    <tr>
                        <th>任务ID</th>
                        <th>竞赛模块</th>
                        <th>工位范围</th>
                        <th>状态</th>
                        <th>进度</th>
                        <th>开始时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>#2025-001</td>
                        <td>基础设施编程</td>
                        <td>1-50</td>
                        <td><span class="status-badge status-completed">已完成</span></td>
                        <td>100%</td>
                        <td>09:00</td>
                        <td>
                            <button class="btn btn-small btn-secondary" onclick="alert('功能演示：查看评分详情')">查看</button>
                        </td>
                    </tr>
                    <tr>
                        <td>#2025-002</td>
                        <td>自动化运维</td>
                        <td>51-100</td>
                        <td><span class="status-badge status-running">评分中</span></td>
                        <td>78%</td>
                        <td>10:30</td>
                        <td>
                            <button class="btn btn-small btn-secondary" onclick="alert('功能演示：暂停评分')">暂停</button>
                        </td>
                    </tr>
                    <tr>
                        <td>#2025-003</td>
                        <td>故障排除</td>
                        <td>101-150</td>
                        <td><span class="status-badge status-pending">待评分</span></td>
                        <td>0%</td>
                        <td>-</td>
                        <td>
                            <button class="btn btn-small btn-primary" onclick="alert('功能演示：开始评分')">开始</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 评分配置 -->
        <div class="scoring-config">
            <h2>评分模块配置</h2>
            <div class="config-grid">
                <div class="config-item">
                    <h4>基础设施编程模块</h4>
                    <p>支持Python、Shell、Ansible等脚本自动评分</p>
                    <div class="config-actions">
                        <button class="btn btn-small btn-secondary" onclick="alert('功能演示：编辑评分规则')">编辑规则</button>
                        <button class="btn btn-small btn-secondary" onclick="alert('功能演示：测试评分')">测试</button>
                    </div>
                </div>
                <div class="config-item">
                    <h4>自动化运维模块</h4>
                    <p>CI/CD流程、容器化部署等任务评分</p>
                    <div class="config-actions">
                        <button class="btn btn-small btn-secondary" onclick="alert('功能演示：编辑评分规则')">编辑规则</button>
                        <button class="btn btn-small btn-secondary" onclick="alert('功能演示：测试评分')">测试</button>
                    </div>
                </div>
                <div class="config-item">
                    <h4>网络系统管理模块</h4>
                    <p>网络配置、服务部署、系统优化评分</p>
                    <div class="config-actions">
                        <button class="btn btn-small btn-secondary" onclick="alert('功能演示：编辑评分规则')">编辑规则</button>
                        <button class="btn btn-small btn-secondary" onclick="alert('功能演示：测试评分')">测试</button>
                    </div>
                </div>
                <div class="config-item">
                    <h4>故障排除模块</h4>
                    <p>故障诊断、问题修复、性能优化评分</p>
                    <div class="config-actions">
                        <button class="btn btn-small btn-secondary" onclick="alert('功能演示：编辑评分规则')">编辑规则</button>
                        <button class="btn btn-small btn-secondary" onclick="alert('功能演示：测试评分')">测试</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
