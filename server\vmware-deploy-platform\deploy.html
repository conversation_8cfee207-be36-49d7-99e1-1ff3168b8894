<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部署中心 - VMware 虚拟机自动部署平台</title>
    <link rel="stylesheet" href="css/normalize.css">
    <link rel="stylesheet" href="css/style.css">
    <!-- 添加一个简单的网站图标 -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💻</text></svg>">
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="container navbar-container">
            <a href="dashboard.html" class="navbar-brand">
                <!-- 使用内联SVG作为临时logo -->
                <svg width="120" height="32" viewBox="0 0 120 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="32" height="32" rx="4" fill="white" fill-opacity="0.2"/>
                    <path d="M8 16L16 8L24 16L16 24L8 16Z" fill="white"/>
                    <path d="M16 8V24" stroke="white" stroke-width="2"/>
                    <path d="M8 16H24" stroke="white" stroke-width="2"/>
                    <text x="36" y="22" font-family="'Segoe UI', Arial, sans-serif" font-size="14" font-weight="600" fill="white">VM Deploy</text>
                </svg>
            </a>
            <ul class="navbar-nav">
                <li class="navbar-item">
                    <a href="#" class="navbar-link">帮助</a>
                </li>
                <li class="navbar-item">
                    <a href="#" class="navbar-link">设置</a>
                </li>
                <li class="navbar-item">
                    <a href="index.html" class="navbar-link">退出</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <ul class="sidebar-nav">
            <li class="sidebar-item">
                <a href="dashboard.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="3" y="3" width="7" height="7"></rect>
                        <rect x="14" y="3" width="7" height="7"></rect>
                        <rect x="14" y="14" width="7" height="7"></rect>
                        <rect x="3" y="14" width="7" height="7"></rect>
                    </svg>
                    仪表盘
                </a>
            </li>
            <li class="sidebar-item">
                <a href="templates.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10 9 9 9 8 9"></polyline>
                    </svg>
                    模板库
                </a>
            </li>
            <li class="sidebar-item">
                <a href="topology.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="2" y1="12" x2="22" y2="12"></line>
                        <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                    </svg>
                    拓扑设计器
                </a>
            </li>
            <li class="sidebar-item">
                <a href="deploy.html" class="sidebar-link active">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                        <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                        <line x1="12" y1="22.08" x2="12" y2="12"></line>
                    </svg>
                    部署中心
                </a>
            </li>
            <li class="sidebar-item">
                <a href="batch-deploy.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="9" y="2" width="6" height="6"></rect>
                        <rect x="16" y="16" width="6" height="6"></rect>
                        <rect x="2" y="16" width="6" height="6"></rect>
                        <path d="M5 16v-4h14v4"></path>
                        <path d="M12 12V8"></path>
                    </svg>
                    批量部署
                </a>
            </li>
            <li class="sidebar-item">
                <a href="machines.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                        <line x1="8" y1="21" x2="16" y2="21"></line>
                        <line x1="12" y1="17" x2="12" y2="21"></line>
                    </svg>
                    虚拟机管理
                </a>
            </li>
            <li class="sidebar-item">
                <a href="monitoring.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                    </svg>
                    资源监控
                </a>
            </li>
            <li class="sidebar-item">
                <a href="users.html" class="sidebar-link">
                    <svg class="sidebar-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                    用户管理
                </a>
            </li>
        </ul>
    </aside>

    <!-- 主内容区域 -->
    <main class="main">
        <div class="container">
            <h1>部署中心</h1>
            <p>创建并配置新的虚拟机。</p>

            <!-- 部署表单 -->
            <div class="card form-card">
                <div class="card-header">
                    <h2 class="card-title">新建虚拟机</h2>
                </div>
                <div class="card-body">
                    <form action="#" method="post">
                        <!-- 基本信息 -->
                        <div style="margin-bottom: 24px;">
                            <h3>基本信息</h3>
                            <div class="form-group">
                                <label for="vm-name" class="form-label">虚拟机名称 *</label>
                                <input type="text" id="vm-name" name="vm-name" class="form-control" placeholder="输入虚拟机名称" required>
                                <small style="color: var(--text-light);">名称只能包含字母、数字和连字符</small>
                            </div>
                            <div class="form-group">
                                <label for="vm-description" class="form-label">描述</label>
                                <textarea id="vm-description" name="vm-description" class="form-control" rows="3" placeholder="输入虚拟机描述（可选）"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="vm-template" class="form-label">选择模板 *</label>
                                <select id="vm-template" name="vm-template" class="form-control" required>
                                    <option value="">-- 选择模板 --</option>
                                    <option value="ubuntu-server-22.04">Ubuntu Server 22.04 LTS</option>
                                    <option value="windows-server-2019">Windows Server 2019</option>
                                    <option value="centos-8">CentOS 8</option>
                                    <option value="debian-11">Debian 11</option>
                                    <option value="windows-10-pro">Windows 10 Pro</option>
                                    <option value="ubuntu-desktop-22.04">Ubuntu Desktop 22.04 LTS</option>
                                </select>
                            </div>
                        </div>

                        <!-- 硬件配置 -->
                        <div style="margin-bottom: 24px;">
                            <h3>硬件配置</h3>
                            <div class="form-group">
                                <label for="vm-cpu" class="form-label">CPU核心数 *</label>
                                <select id="vm-cpu" name="vm-cpu" class="form-control" required>
                                    <option value="1">1核</option>
                                    <option value="2" selected>2核</option>
                                    <option value="4">4核</option>
                                    <option value="8">8核</option>
                                    <option value="16">16核</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="vm-memory" class="form-label">内存 *</label>
                                <select id="vm-memory" name="vm-memory" class="form-control" required>
                                    <option value="1">1 GB</option>
                                    <option value="2">2 GB</option>
                                    <option value="4" selected>4 GB</option>
                                    <option value="8">8 GB</option>
                                    <option value="16">16 GB</option>
                                    <option value="32">32 GB</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="vm-disk" class="form-label">磁盘空间 *</label>
                                <select id="vm-disk" name="vm-disk" class="form-control" required>
                                    <option value="20">20 GB</option>
                                    <option value="50" selected>50 GB</option>
                                    <option value="100">100 GB</option>
                                    <option value="250">250 GB</option>
                                    <option value="500">500 GB</option>
                                    <option value="1000">1 TB</option>
                                </select>
                            </div>
                        </div>

                        <!-- 网络配置 -->
                        <div style="margin-bottom: 24px;">
                            <h3>网络配置</h3>
                            <div class="form-group">
                                <label for="vm-network" class="form-label">网络类型 *</label>
                                <select id="vm-network" name="vm-network" class="form-control" required>
                                    <option value="bridged" selected>桥接模式</option>
                                    <option value="nat">NAT模式</option>
                                    <option value="host-only">仅主机模式</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <div style="display: flex; align-items: center;">
                                    <input type="checkbox" id="vm-static-ip" name="vm-static-ip" style="margin-right: 8px;">
                                    <label for="vm-static-ip" class="form-label" style="margin-bottom: 0;">使用静态IP</label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="vm-ip" class="form-label">IP地址</label>
                                <input type="text" id="vm-ip" name="vm-ip" class="form-control" placeholder="例如：*************" disabled>
                            </div>
                        </div>

                        <!-- 高级选项 -->
                        <div style="margin-bottom: 24px;">
                            <h3>高级选项</h3>
                            <div class="form-group">
                                <div style="display: flex; align-items: center;">
                                    <input type="checkbox" id="vm-start-after-create" name="vm-start-after-create" checked style="margin-right: 8px;">
                                    <label for="vm-start-after-create" class="form-label" style="margin-bottom: 0;">创建后立即启动</label>
                                </div>
                            </div>
                            <div class="form-group">
                                <div style="display: flex; align-items: center;">
                                    <input type="checkbox" id="vm-auto-restart" name="vm-auto-restart" style="margin-right: 8px;">
                                    <label for="vm-auto-restart" class="form-label" style="margin-bottom: 0;">宿主机重启后自动启动</label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="vm-snapshot" class="form-label">自动快照</label>
                                <select id="vm-snapshot" name="vm-snapshot" class="form-control">
                                    <option value="none" selected>不创建</option>
                                    <option value="daily">每天</option>
                                    <option value="weekly">每周</option>
                                    <option value="monthly">每月</option>
                                </select>
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="form-group" style="margin-top: 32px;">
                            <button type="submit" class="btn btn-primary" style="margin-right: 16px;">部署虚拟机</button>
                            <button type="reset" class="btn" style="background-color: var(--bg-light); border-color: var(--border-color);">重置</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <!-- 简单的JavaScript来模拟静态IP输入框的启用/禁用 -->
    <script>
        document.getElementById('vm-static-ip').addEventListener('change', function() {
            document.getElementById('vm-ip').disabled = !this.checked;
        });
    </script>
</body>
</html> 