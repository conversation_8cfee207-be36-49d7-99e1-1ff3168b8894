<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工位批量管理 - 网络系统管理竞赛自动化运维平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f5f7fa;
            color: #2c3e50;
        }

        /* 导航栏 */
        .navbar {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            font-size: 20px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .navbar-user {
            display: flex;
            align-items: center;
            gap: 16px;
            color: #666;
            font-size: 14px;
        }

        .navbar-user a {
            color: #3182ce;
            text-decoration: none;
        }

        /* 侧边栏 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            bottom: 0;
            width: 240px;
            background: white;
            border-right: 1px solid #e2e8f0;
            padding: 24px 0;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 4px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.2s;
            font-size: 14px;
        }

        .sidebar-menu a:hover {
            background: #f7fafc;
            color: #1a1a1a;
        }

        .sidebar-menu a.active {
            background: #e6f2ff;
            color: #3182ce;
            font-weight: 500;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 240px;
            padding: 32px;
        }

        .page-header {
            margin-bottom: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-header h1 {
            font-size: 28px;
            font-weight: 600;
            color: #1a1a1a;
        }

        /* 工位管理区域 */
        .workstation-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stat-card h3 {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .stat-card .value {
            font-size: 32px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .stat-card .status {
            font-size: 12px;
            margin-top: 8px;
        }

        .stat-card .status.online {
            color: #10b981;
        }

        .stat-card .status.offline {
            color: #ef4444;
        }

        /* 批量操作面板 */
        .batch-operations {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .batch-operations h2 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .operation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: #1a1a1a;
            color: white;
        }

        .btn-primary:hover {
            background: #2d2d2d;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: white;
            color: #1a1a1a;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #f7fafc;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        /* 工位选择区域 */
        .workstation-selection {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .selection-controls {
            display: flex;
            gap: 16px;
            align-items: center;
            margin-bottom: 20px;
        }

        .selection-controls input {
            padding: 8px 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 14px;
        }

        /* 工位网格 */
        .workstation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 10px;
            max-height: 400px;
            overflow-y: auto;
            padding: 10px;
            background: #f5f7fa;
            border-radius: 6px;
        }

        .workstation-item {
            aspect-ratio: 1;
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }

        .workstation-item:hover {
            border-color: #3182ce;
            transform: translateY(-2px);
        }

        .workstation-item.selected {
            background: #3182ce;
            color: white;
            border-color: #3182ce;
        }

        .workstation-item.online::after {
            content: "";
            position: absolute;
            top: 4px;
            right: 4px;
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
        }

        .workstation-number {
            font-size: 14px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="navbar-brand">网络系统管理竞赛自动化运维平台</div>
        <div class="navbar-user">
            <span>管理员</span>
            <a href="index.html">退出</a>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html">📊 仪表板</a></li>
            <li><a href="competition-env.html">🏆 竞赛环境管理</a></li>
            <li><a href="auto-deploy.html">🚀 一键部署</a></li>
            <li><a href="batch-workstation.html" class="active">💻 工位批量管理</a></li>
            <li><a href="scoring-system.html">📝 自动评分系统</a></li>
            <li><a href="fault-management.html">🔧 故障排除模块</a></li>
            <li><a href="module-management.html">📦 模块管理</a></li>
            <li><a href="environment-restore.html">🔄 环境还原</a></li>
            <li><a href="monitoring.html">📈 监控中心</a></li>
        </ul>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="page-header">
            <h1>工位批量管理</h1>
            <button class="btn btn-primary" onclick="alert('功能演示：添加新的工位组')">
                <span>➕</span>
                <span>添加工位组</span>
            </button>
        </div>

        <!-- 工位统计 -->
        <div class="workstation-stats">
            <div class="stat-card">
                <h3>总工位数</h3>
                <div class="value">200</div>
                <div class="status">支持100+工位同时管理</div>
            </div>
            <div class="stat-card">
                <h3>在线工位</h3>
                <div class="value">156</div>
                <div class="status online">78% 在线率</div>
            </div>
            <div class="stat-card">
                <h3>正在部署</h3>
                <div class="value">12</div>
                <div class="status">预计5分钟完成</div>
            </div>
            <div class="stat-card">
                <h3>离线工位</h3>
                <div class="value">44</div>
                <div class="status offline">需要检查</div>
            </div>
        </div>

        <!-- 批量操作 -->
        <div class="batch-operations">
            <h2>批量操作</h2>
            <div class="operation-grid">
                <button class="btn btn-primary" onclick="alert('功能演示：批量部署选中的工位')">
                    <span>🚀</span>
                    <span>批量部署</span>
                </button>
                <button class="btn btn-secondary" onclick="alert('功能演示：批量更新工位配置')">
                    <span>🔄</span>
                    <span>批量更新</span>
                </button>
                <button class="btn btn-secondary" onclick="alert('功能演示：批量重启工位')">
                    <span>🔁</span>
                    <span>批量重启</span>
                </button>
                <button class="btn btn-secondary" onclick="alert('功能演示：批量监控工位状态')">
                    <span>📊</span>
                    <span>状态监控</span>
                </button>
                <button class="btn btn-secondary" onclick="alert('功能演示：批量配置网络')">
                    <span>🌐</span>
                    <span>网络配置</span>
                </button>
                <button class="btn btn-danger" onclick="alert('功能演示：批量停止工位')">
                    <span>⏹️</span>
                    <span>批量停止</span>
                </button>
            </div>
        </div>

        <!-- 工位选择 -->
        <div class="workstation-selection">
            <h2>工位选择</h2>
            <div class="selection-controls">
                <button class="btn btn-secondary" onclick="selectAll()">全选</button>
                <button class="btn btn-secondary" onclick="selectNone()">取消全选</button>
                <input type="text" placeholder="输入工位范围，如: 1-50" onkeyup="selectRange(this.value)">
                <span style="margin-left: auto; color: #666;">已选择: <span id="selectedCount">0</span> 个工位</span>
            </div>
            <div class="workstation-grid" id="workstationGrid">
                <!-- 动态生成工位 -->
            </div>
        </div>
    </div>

    <script>
        // 初始化工位网格
        function initWorkstationGrid() {
            const grid = document.getElementById('workstationGrid');
            for (let i = 1; i <= 200; i++) {
                const item = document.createElement('div');
                item.className = 'workstation-item';
                // 随机设置一些工位为在线状态
                if (Math.random() > 0.22) {
                    item.classList.add('online');
                }
                item.innerHTML = `<span class="workstation-number">${i}</span>`;
                item.onclick = () => toggleSelection(item);
                grid.appendChild(item);
            }
        }

        // 切换选择状态
        function toggleSelection(item) {
            item.classList.toggle('selected');
            updateSelectedCount();
        }

        // 更新选中数量
        function updateSelectedCount() {
            const count = document.querySelectorAll('.workstation-item.selected').length;
            document.getElementById('selectedCount').textContent = count;
        }

        // 全选
        function selectAll() {
            document.querySelectorAll('.workstation-item').forEach(item => {
                item.classList.add('selected');
            });
            updateSelectedCount();
        }

        // 取消全选
        function selectNone() {
            document.querySelectorAll('.workstation-item').forEach(item => {
                item.classList.remove('selected');
            });
            updateSelectedCount();
        }

        // 选择范围
        function selectRange(value) {
            if (!value) return;
            
            const match = value.match(/(\d+)-(\d+)/);
            if (match) {
                const start = parseInt(match[1]);
                const end = parseInt(match);
                
                document.querySelectorAll('.workstation-item').forEach((item, index) => {
                    const num = index + 1;
                    if (num >= start && num <= end) {
                        item.classList.add('selected');
                    } else {
                        item.classList.remove('selected');
                    }
                });
                updateSelectedCount();
            }
        }

        // 初始化
        initWorkstationGrid();
    </script>
</body>
</html>
