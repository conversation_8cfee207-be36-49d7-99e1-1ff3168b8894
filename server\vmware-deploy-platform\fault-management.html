<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故障排除模块 - 网络系统管理竞赛自动化运维平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f5f7fa;
            color: #2c3e50;
        }

        /* 导航栏 */
        .navbar {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            font-size: 20px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .navbar-user {
            display: flex;
            align-items: center;
            gap: 16px;
            color: #666;
            font-size: 14px;
        }

        .navbar-user a {
            color: #3182ce;
            text-decoration: none;
        }

        /* 侧边栏 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            bottom: 0;
            width: 240px;
            background: white;
            border-right: 1px solid #e2e8f0;
            padding: 24px 0;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 4px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.2s;
            font-size: 14px;
        }

        .sidebar-menu a:hover {
            background: #f7fafc;
            color: #1a1a1a;
        }

        .sidebar-menu a.active {
            background: #e6f2ff;
            color: #3182ce;
            font-weight: 500;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 240px;
            padding: 32px;
        }

        .page-header {
            margin-bottom: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-header h1 {
            font-size: 28px;
            font-weight: 600;
            color: #1a1a1a;
        }

        /* 故障分类 */
        .fault-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .category-card {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.2s;
            border: 2px solid transparent;
        }

        .category-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #3182ce;
        }

        .category-card.active {
            border-color: #3182ce;
            background: #f0f9ff;
        }

        .category-icon {
            font-size: 32px;
            margin-bottom: 12px;
        }

        .category-name {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 8px;
        }

        .category-count {
            font-size: 14px;
            color: #666;
        }

        /* 故障列表 */
        .fault-section {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .section-header h2 {
            font-size: 20px;
            font-weight: 600;
            color: #1a1a1a;
        }

        /* 故障项 */
        .fault-list {
            display: grid;
            gap: 16px;
        }

        .fault-item {
            padding: 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            transition: all 0.2s;
        }

        .fault-item:hover {
            border-color: #3182ce;
            background: #f7fafc;
        }

        .fault-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .fault-title {
            font-size: 16px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .fault-level {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .level-easy {
            background: #d1fae5;
            color: #10b981;
        }

        .level-medium {
            background: #fef3c7;
            color: #f59e0b;
        }

        .level-hard {
            background: #fee2e2;
            color: #ef4444;
        }

        .fault-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
        }

        .fault-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-primary {
            background: #1a1a1a;
            color: white;
        }

        .btn-primary:hover {
            background: #2d2d2d;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: white;
            color: #1a1a1a;
            border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #f7fafc;
        }

        /* 批量部署 */
        .batch-deploy {
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .deploy-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }

        .btn-large {
            padding: 16px 24px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="navbar-brand">网络系统管理竞赛自动化运维平台</div>
        <div class="navbar-user">
            <span>管理员</span>
            <a href="index.html">退出</a>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html">📊 仪表板</a></li>
            <li><a href="competition-env.html">🏆 竞赛环境管理</a></li>
            <li><a href="auto-deploy.html">🚀 一键部署</a></li>
            <li><a href="batch-workstation.html">💻 工位批量管理</a></li>
            <li><a href="scoring-system.html">📝 自动评分系统</a></li>
            <li><a href="fault-management.html" class="active">🔧 故障排除模块</a></li>
            <li><a href="module-management.html">📦 模块管理</a></li>
            <li><a href="environment-restore.html">🔄 环境还原</a></li>
            <li><a href="monitoring.html">📈 监控中心</a></li>
        </ul>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="page-header">
            <h1>故障排除模块</h1>
            <button class="btn btn-primary" onclick="alert('功能演示：创建新的故障项')">
                <span>➕</span>
                <span>创建故障项</span>
            </button>
        </div>

        <!-- 故障分类 -->
        <div class="fault-categories">
            <div class="category-card active" onclick="selectCategory(this)">
                <div class="category-icon">🌐</div>
                <div class="category-name">网络配置故障</div>
                <div class="category-count">15个故障项</div>
            </div>
            <div class="category-card" onclick="selectCategory(this)">
                <div class="category-icon">💾</div>
                <div class="category-name">系统服务故障</div>
                <div class="category-count">12个故障项</div>
            </div>
            <div class="category-card" onclick="selectCategory(this)">
                <div class="category-icon">⚡</div>
                <div class="category-name">性能问题</div>
                <div class="category-count">8个故障项</div>
            </div>
            <div class="category-card" onclick="selectCategory(this)">
                <div class="category-icon">🔐</div>
                <div class="category-name">安全配置问题</div>
                <div class="category-count">10个故障项</div>
            </div>
        </div>

        <!-- 故障项列表 -->
        <div class="fault-section">
            <div class="section-header">
                <h2>网络配置故障</h2>
                <button class="btn btn-secondary" onclick="alert('功能演示：刷新故障列表')">
                    <span>🔄</span>
                    <span>刷新</span>
                </button>
            </div>
            <div class="fault-list">
                <div class="fault-item">
                    <div class="fault-item-header">
                        <h3 class="fault-title">DNS解析异常</h3>
                        <span class="fault-level level-medium">中等</span>
                    </div>
                    <p class="fault-description">系统无法解析域名，需要检查DNS配置文件和网络连接状态</p>
                    <div class="fault-actions">
                        <button class="btn btn-primary" onclick="alert('功能演示：部署此故障到选定工位')">部署</button>
                        <button class="btn btn-secondary" onclick="alert('功能演示：编辑故障详情')">编辑</button>
                        <button class="btn btn-secondary" onclick="alert('功能演示：查看解决方案')">方案</button>
                    </div>
                </div>

                <div class="fault-item">
                    <div class="fault-item-header">
                        <h3 class="fault-title">路由表配置错误</h3>
                        <span class="fault-level level-hard">困难</span>
                    </div>
                    <p class="fault-description">静态路由配置错误导致网络不通，需要重新配置路由表</p>
                    <div class="fault-actions">
                        <button class="btn btn-primary" onclick="alert('功能演示：部署此故障到选定工位')">部署</button>
                        <button class="btn btn-secondary" onclick="alert('功能演示：编辑故障详情')">编辑</button>
                        <button class="btn btn-secondary" onclick="alert('功能演示：查看解决方案')">方案</button>
                    </div>
                </div>

                <div class="fault-item">
                    <div class="fault-item-header">
                        <h3 class="fault-title">网卡配置错误</h3>
                        <span class="fault-level level-easy">简单</span>
                    </div>
                    <p class="fault-description">网络接口配置文件中IP地址或子网掩码错误</p>
                    <div class="fault-actions">
                        <button class="btn btn-primary" onclick="alert('功能演示：部署此故障到选定工位')">部署</button>
                        <button class="btn btn-secondary" onclick="alert('功能演示：编辑故障详情')">编辑</button>
                        <button class="btn btn-secondary" onclick="alert('功能演示：查看解决方案')">方案</button>
                    </div>
                </div>

                <div class="fault-item">
                    <div class="fault-item-header">
                        <h3 class="fault-title">防火墙规则异常</h3>
                        <span class="fault-level level-medium">中等</span>
                    </div>
                    <p class="fault-description">防火墙规则配置不当导致服务无法访问</p>
                    <div class="fault-actions">
                        <button class="btn btn-primary" onclick="alert('功能演示：部署此故障到选定工位')">部署</button>
                        <button class="btn btn-secondary" onclick="alert('功能演示：编辑故障详情')">编辑</button>
                        <button class="btn btn-secondary" onclick="alert('功能演示：查看解决方案')">方案</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 批量部署 -->
        <div class="batch-deploy">
            <h2>批量故障部署</h2>
            <div class="deploy-options">
                <button class="btn btn-primary btn-large" onclick="alert('功能演示：部署所有故障到选定工位')">
                    <span>🚀</span>
                    <span>部署所有故障</span>
                </button>
                <button class="btn btn-secondary btn-large" onclick="alert('功能演示：随机部署故障')">
                    <span>🎲</span>
                    <span>随机部署</span>
                </button>
                <button class="btn btn-secondary btn-large" onclick="alert('功能演示：按难度部署')">
                    <span>📊</span>
                    <span>按难度部署</span>
                </button>
                <button class="btn btn-secondary btn-large" onclick="alert('功能演示：自定义部署方案')">
                    <span>⚙️</span>
                    <span>自定义方案</span>
                </button>
            </div>
        </div>
    </div>

    <script>
        function selectCategory(element) {
            // 移除所有选中状态
            document.querySelectorAll('.category-card').forEach(card => {
                card.classList.remove('active');
            });
            // 添加选中状态
            element.classList.add('active');
            
            // 这里可以加载对应类别的故障项
            alert('功能演示：切换到 ' + element.querySelector('.category-name').textContent);
        }
    </script>
</body>
</html>
