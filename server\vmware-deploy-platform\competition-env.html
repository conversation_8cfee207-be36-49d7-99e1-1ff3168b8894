<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>竞赛环境管理 - 网络系统管理竞赛自动化运维平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f5f7fa;
            color: #2c3e50;
        }

        /* 导航栏 */
        .navbar {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            font-size: 20px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .navbar-user {
            display: flex;
            align-items: center;
            gap: 16px;
            color: #666;
            font-size: 14px;
        }

        .navbar-user a {
            color: #3182ce;
            text-decoration: none;
        }

        /* 侧边栏 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            bottom: 0;
            width: 240px;
            background: white;
            border-right: 1px solid #e2e8f0;
            padding: 24px 0;
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 4px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.2s;
            font-size: 14px;
        }

        .sidebar-menu a:hover {
            background: #f7fafc;
            color: #1a1a1a;
        }

        .sidebar-menu a.active {
            background: #e6f2ff;
            color: #3182ce;
            font-weight: 500;
        }

        /* 主内容区 */
        .main-content {
            margin-left: 240px;
            padding: 32px;
        }

        .page-header {
            margin-bottom: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-header h1 {
            font-size: 28px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #1a1a1a;
            color: white;
        }

        .btn-primary:hover {
            background: #2d2d2d;
            transform: translateY(-1px);
        }

        /* 竞赛环境卡片 */
        .env-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 24px;
        }

        .env-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.2s;
        }

        .env-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .env-header {
            padding: 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .env-title {
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
        }

        .env-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-ready {
            background: #d1fae5;
            color: #059669;
        }

        .status-preparing {
            background: #fef3c7;
            color: #d97706;
        }

        .env-body {
            padding: 20px;
        }

        .env-info {
            margin-bottom: 20px;
        }

        .env-info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            font-size: 14px;
            color: #666;
        }

        .env-info-item strong {
            color: #1a1a1a;
        }

        .env-actions {
            display: flex;
            gap: 12px;
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 13px;
            border: 1px solid #e2e8f0;
            background: white;
            color: #1a1a1a;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            flex: 1;
            text-align: center;
            text-decoration: none;
        }

        .btn-small:hover {
            background: #f7fafc;
        }

        .btn-small.primary {
            background: #3182ce;
            color: white;
            border-color: #3182ce;
        }

        .btn-small.primary:hover {
            background: #2563eb;
        }

        /* 支持的赛事列表 */
        .supported-events {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 24px;
            margin-top: 32px;
        }

        .supported-events h2 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #1a1a1a;
        }

        .event-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 16px;
        }

        .event-item {
            padding: 16px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.2s;
        }

        .event-item:hover {
            background: #f7fafc;
            border-color: #3182ce;
        }

        .event-icon {
            font-size: 24px;
        }

        .event-info h3 {
            font-size: 16px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 4px;
        }

        .event-info p {
            font-size: 13px;
            color: #666;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="navbar-brand">网络系统管理竞赛自动化运维平台</div>
        <div class="navbar-user">
            <span>管理员</span>
            <a href="index.html">退出</a>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <div class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="dashboard.html">📊 仪表板</a></li>
            <li><a href="competition-env.html" class="active">🏆 竞赛环境管理</a></li>
            <li><a href="auto-deploy.html">🚀 一键部署</a></li>
            <li><a href="batch-workstation.html">💻 工位批量管理</a></li>
            <li><a href="scoring-system.html">📝 自动评分系统</a></li>
            <li><a href="fault-management.html">🔧 故障排除模块</a></li>
            <li><a href="module-management.html">📦 模块管理</a></li>
            <li><a href="environment-restore.html">🔄 环境还原</a></li>
            <li><a href="monitoring.html">📈 监控中心</a></li>
        </ul>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="page-header">
            <h1>竞赛环境管理</h1>
            <button class="btn btn-primary" onclick="alert('功能演示：创建新的竞赛环境配置，支持自定义模块和参数设置')">
                <span>➕</span>
                <span>创建竞赛环境</span>
            </button>
        </div>

        <!-- 竞赛环境列表 -->
        <div class="env-grid">
            <div class="env-card">
                <div class="env-header">
                    <h3 class="env-title">世界技能大赛 - 网络系统管理</h3>
                    <span class="env-status status-ready">就绪</span>
                </div>
                <div class="env-body">
                    <div class="env-info">
                        <div class="env-info-item">
                            <span>环境版本</span>
                            <strong>WSC2024-v2.1</strong>
                        </div>
                        <div class="env-info-item">
                            <span>包含模块</span>
                            <strong>基础设施、编程、故障排除</strong>
                        </div>
                        <div class="env-info-item">
                            <span>工位数量</span>
                            <strong>150 套</strong>
                        </div>
                        <div class="env-info-item">
                            <span>最后更新</span>
                            <strong>2025-01-20</strong>
                        </div>
                    </div>
                    <div class="env-actions">
                        <button class="btn-small primary" onclick="alert('功能演示：一键部署世界技能大赛网络系统管理竞赛环境，包含所有必需的基础设施、编程和故障排除模块')">部署</button>
                        <button class="btn-small" onclick="alert('功能演示：配置环境参数，包括网络设置、资源分配、模块选择等')">配置</button>
                        <button class="btn-small" onclick="alert('功能演示：查看环境详细信息，包括拓扑图、配置清单、部署历史等')">详情</button>
                    </div>
                </div>
            </div>

            <div class="env-card">
                <div class="env-header">
                    <h3 class="env-title">全国职业院校技能大赛</h3>
                    <span class="env-status status-ready">就绪</span>
                </div>
                <div class="env-body">
                    <div class="env-info">
                        <div class="env-info-item">
                            <span>环境版本</span>
                            <strong>NVSC2024-v1.5</strong>
                        </div>
                        <div class="env-info-item">
                            <span>包含模块</span>
                            <strong>网络配置、自动化运维</strong>
                        </div>
                        <div class="env-info-item">
                            <span>工位数量</span>
                            <strong>120 套</strong>
                        </div>
                        <div class="env-info-item">
                            <span>最后更新</span>
                            <strong>2025-01-18</strong>
                        </div>
                    </div>
                    <div class="env-actions">
                        <button class="btn-small primary" onclick="alert('功能演示：部署全国职业院校技能大赛竞赛环境')">部署</button>
                        <button class="btn-small" onclick="alert('功能演示：配置大赛专用参数')">配置</button>
                        <button class="btn-small" onclick="alert('功能演示：查看大赛环境详情')">详情</button>
                    </div>
                </div>
            </div>

            <div class="env-card">
                <div class="env-header">
                    <h3 class="env-title">省级技能竞赛 - 网络系统管理</h3>
                    <span class="env-status status-preparing">准备中</span>
                </div>
                <div class="env-body">
                    <div class="env-info">
                        <div class="env-info-item">
                            <span>环境版本</span>
                            <strong>PSC2025-v1.0</strong>
                        </div>
                        <div class="env-info-item">
                            <span>包含模块</span>
                            <strong>基础设施编程、自动化运维</strong>
                        </div>
                        <div class="env-info-item">
                            <span>工位数量</span>
                            <strong>100 套</strong>
                        </div>
                        <div class="env-info-item">
                            <span>最后更新</span>
                            <strong>2025-01-22</strong>
                        </div>
                    </div>
                    <div class="env-actions">
                        <button class="btn-small" onclick="alert('功能演示：环境正在准备中，预计2小时后可用')">准备中</button>
                        <button class="btn-small" onclick="alert('功能演示：预览环境配置')">预览</button>
                        <button class="btn-small" onclick="alert('功能演示：查看准备进度')">进度</button>
                    </div>
                </div>
            </div>

            <div class="env-card">
                <div class="env-header">
                    <h3 class="env-title">行业技能竞赛 - 自动化运维</h3>
                    <span class="env-status status-ready">就绪</span>
                </div>
                <div class="env-body">
                    <div class="env-info">
                        <div class="env-info-item">
                            <span>环境版本</span>
                            <strong>ISC2024-v3.2</strong>
                        </div>
                        <div class="env-info-item">
                            <span>包含模块</span>
                            <strong>自动化脚本、容器编排、CI/CD</strong>
                        </div>
                        <div class="env-info-item">
                            <span>工位数量</span>
                            <strong>80 套</strong>
                        </div>
                        <div class="env-info-item">
                            <span>最后更新</span>
                            <strong>2025-01-15</strong>
                        </div>
                    </div>
                    <div class="env-actions">
                        <button class="btn-small primary" onclick="alert('功能演示：部署行业技能竞赛自动化运维环境')">部署</button>
                        <button class="btn-small" onclick="alert('功能演示：自定义行业特色配置')">配置</button>
                        <button class="btn-small" onclick="alert('功能演示：查看详细架构图')">详情</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 支持的赛事 -->
        <div class="supported-events">
            <h2>支持的竞赛类型</h2>
            <div class="event-list">
                <div class="event-item">
                    <span class="event-icon">🌍</span>
                    <div class="event-info">
                        <h3>世界技能大赛</h3>
                        <p>完整支持WSC标准，包含所有竞赛模块</p>
                    </div>
                </div>
                <div class="event-item">
                    <span class="event-icon">🇨🇳</span>
                    <div class="event-info">
                        <h3>全国技能大赛</h3>
                        <p>国赛标准环境，自动化部署与评分</p>
                    </div>
                </div>
                <div class="event-item">
                    <span class="event-icon">🏫</span>
                    <div class="event-info">
                        <h3>职业院校技能大赛</h3>
                        <p>职教赛项专用环境，模块化配置</p>
                    </div>
                </div>
                <div class="event-item">
                    <span class="event-icon">🏢</span>
                    <div class="event-info">
                        <h3>行业技能竞赛</h3>
                        <p>企业级标准，实战化环境部署</p>
                    </div>
                </div>
                <div class="event-item">
                    <span class="event-icon">🏛️</span>
                    <div class="event-info">
                        <h3>省市级竞赛</h3>
                        <p>地方赛事支持，灵活定制化</p>
                    </div>
                </div>
                <div class="event-item">
                    <span class="event-icon">🎓</span>
                    <div class="event-info">
                        <h3>校园技能竞赛</h3>
                        <p>教学训练环境，循序渐进式配置</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
